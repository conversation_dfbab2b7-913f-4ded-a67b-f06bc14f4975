const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Configuration file path
const configPath = path.join(__dirname, '../../config/ticket-config.json');

// Ensure config directory exists
const configDir = path.dirname(configPath);
if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
}

// Default configuration
const defaultConfig = {
    rolesSupport: {
        enabled: false,
        disabledMessage: '❌ **Roles Support is temporarily disabled**\n\n🔒 The roles support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent roles-related issue.'
    }
};

// Load configuration
function loadConfig() {
    try {
        if (fs.existsSync(configPath)) {
            const data = fs.readFileSync(configPath, 'utf8');
            return { ...defaultConfig, ...JSON.parse(data) };
        }
    } catch (error) {
        console.error('Error loading ticket config:', error);
    }
    return defaultConfig;
}

// Save configuration
function saveConfig(config) {
    try {
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving ticket config:', error);
        return false;
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('toggle-roles-support')
        .setDescription('Enable or disable roles support tickets')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addStringOption(option =>
            option.setName('action')
                .setDescription('Enable or disable roles support')
                .setRequired(true)
                .addChoices(
                    { name: 'Enable', value: 'enable' },
                    { name: 'Disable', value: 'disable' },
                    { name: 'Status', value: 'status' }
                ))
        .addStringOption(option =>
            option.setName('message')
                .setDescription('Custom disabled message (optional)')
                .setRequired(false)
                .setMaxLength(500)),

    async execute(interaction) {
        const action = interaction.options.getString('action');
        const customMessage = interaction.options.getString('message');
        
        const config = loadConfig();
        
        switch (action) {
            case 'enable':
                config.rolesSupport.enabled = true;
                if (saveConfig(config)) {
                    const embed = new EmbedBuilder()
                        .setColor('#2ecc71')
                        .setTitle('✅ Roles Support Enabled')
                        .setDescription('Users can now create roles support tickets.')
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } else {
                    await interaction.reply({
                        content: '❌ Failed to save configuration. Please try again.',
                        ephemeral: true
                    });
                }
                break;
                
            case 'disable':
                config.rolesSupport.enabled = false;
                if (customMessage) {
                    config.rolesSupport.disabledMessage = customMessage;
                }
                
                if (saveConfig(config)) {
                    const embed = new EmbedBuilder()
                        .setColor('#e74c3c')
                        .setTitle('🔒 Roles Support Disabled')
                        .setDescription('Users can no longer create roles support tickets.')
                        .addFields({
                            name: 'Disabled Message',
                            value: config.rolesSupport.disabledMessage,
                            inline: false
                        })
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } else {
                    await interaction.reply({
                        content: '❌ Failed to save configuration. Please try again.',
                        ephemeral: true
                    });
                }
                break;
                
            case 'status':
                const statusEmbed = new EmbedBuilder()
                    .setColor(config.rolesSupport.enabled ? '#2ecc71' : '#e74c3c')
                    .setTitle('📊 Roles Support Status')
                    .addFields(
                        {
                            name: 'Status',
                            value: config.rolesSupport.enabled ? '✅ Enabled' : '🔒 Disabled',
                            inline: true
                        },
                        {
                            name: 'Disabled Message',
                            value: config.rolesSupport.disabledMessage,
                            inline: false
                        }
                    )
                    .setTimestamp();
                
                await interaction.reply({ embeds: [statusEmbed], ephemeral: true });
                break;
        }
    }
};

// Export functions for use in other files
module.exports.loadConfig = loadConfig;
module.exports.saveConfig = saveConfig;
