const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Configuration file path
const configPath = path.join(__dirname, '../../config/ticket-config.json');

// Ensure config directory exists
const configDir = path.dirname(configPath);
if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
}

// Default configuration
const defaultConfig = {
    ticketTypes: {
        roles_support: {
            enabled: false,
            disabledMessage: '❌ **Roles Support is temporarily disabled**\n\n🔒 The roles support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent roles-related issue.'
        },
        kick_support: {
            enabled: true,
            disabledMessage: '❌ **Kick Support is temporarily disabled**\n\n🔒 The kick support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent kick-related issue.'
        },
        staff_ticket: {
            enabled: true,
            disabledMessage: '❌ **Staff Tickets are temporarily disabled**\n\n🔒 The staff ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent staff-related issue.'
        },
        girls_support: {
            enabled: true,
            disabledMessage: '❌ **Girls Support is temporarily disabled**\n\n🔒 The girls support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent girls support issue.'
        },
        complaint: {
            enabled: true,
            disabledMessage: '❌ **Complaint System is temporarily disabled**\n\n🔒 The complaint ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent complaint.'
        },
        server_support: {
            enabled: true,
            disabledMessage: '❌ **Server Support is temporarily disabled**\n\n🔒 The server support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent server-related issue.'
        }
    }
};

// Load configuration
function loadConfig() {
    try {
        if (fs.existsSync(configPath)) {
            const data = fs.readFileSync(configPath, 'utf8');
            return { ...defaultConfig, ...JSON.parse(data) };
        }
    } catch (error) {
        console.error('Error loading ticket config:', error);
    }
    return defaultConfig;
}

// Save configuration
function saveConfig(config) {
    try {
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving ticket config:', error);
        return false;
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('toggle-ticket-type')
        .setDescription('Enable or disable specific ticket types')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addStringOption(option =>
            option.setName('action')
                .setDescription('Action to perform')
                .setRequired(true)
                .addChoices(
                    { name: 'Enable', value: 'enable' },
                    { name: 'Disable', value: 'disable' },
                    { name: 'Status', value: 'status' },
                    { name: 'List All', value: 'list' }
                ))
        .addStringOption(option =>
            option.setName('ticket-type')
                .setDescription('Type of ticket to manage')
                .setRequired(false)
                .addChoices(
                    { name: 'Roles Support', value: 'roles_support' },
                    { name: 'Kick Support', value: 'kick_support' },
                    { name: 'Staff Ticket', value: 'staff_ticket' },
                    { name: 'Girls Support', value: 'girls_support' },
                    { name: 'Complaint', value: 'complaint' },
                    { name: 'Server Support', value: 'server_support' },
                    { name: 'All Types', value: 'all' }
                ))
        .addStringOption(option =>
            option.setName('message')
                .setDescription('Custom disabled message (optional)')
                .setRequired(false)
                .setMaxLength(500)),

    async execute(interaction) {
        const action = interaction.options.getString('action');
        const ticketType = interaction.options.getString('ticket-type');
        const customMessage = interaction.options.getString('message');

        const config = loadConfig();

        // Ticket type display names
        const typeNames = {
            roles_support: 'Roles Support',
            kick_support: 'Kick Support',
            staff_ticket: 'Staff Ticket',
            girls_support: 'Girls Support',
            complaint: 'Complaint',
            server_support: 'Server Support'
        };

        switch (action) {
            case 'enable':
                if (!ticketType || ticketType === 'all') {
                    if (ticketType === 'all') {
                        // Enable all ticket types
                        Object.keys(config.ticketTypes).forEach(type => {
                            config.ticketTypes[type].enabled = true;
                        });

                        if (saveConfig(config)) {
                            const embed = new EmbedBuilder()
                                .setColor('#2ecc71')
                                .setTitle('✅ All Ticket Types Enabled')
                                .setDescription('Users can now create all types of tickets.')
                                .setTimestamp();

                            await interaction.reply({ embeds: [embed], ephemeral: true });
                        } else {
                            await interaction.reply({
                                content: '❌ Failed to save configuration. Please try again.',
                                ephemeral: true
                            });
                        }
                    } else {
                        await interaction.reply({
                            content: '❌ Please specify a ticket type to enable.',
                            ephemeral: true
                        });
                    }
                } else {
                    if (!config.ticketTypes[ticketType]) {
                        return interaction.reply({
                            content: '❌ Invalid ticket type specified.',
                            ephemeral: true
                        });
                    }

                    config.ticketTypes[ticketType].enabled = true;
                    if (saveConfig(config)) {
                        const embed = new EmbedBuilder()
                            .setColor('#2ecc71')
                            .setTitle(`✅ ${typeNames[ticketType]} Enabled`)
                            .setDescription(`Users can now create ${typeNames[ticketType].toLowerCase()} tickets.`)
                            .setTimestamp();

                        await interaction.reply({ embeds: [embed], ephemeral: true });
                    } else {
                        await interaction.reply({
                            content: '❌ Failed to save configuration. Please try again.',
                            ephemeral: true
                        });
                    }
                }
                break;

            case 'disable':
                if (!ticketType || ticketType === 'all') {
                    if (ticketType === 'all') {
                        // Disable all ticket types
                        Object.keys(config.ticketTypes).forEach(type => {
                            config.ticketTypes[type].enabled = false;
                            if (customMessage) {
                                config.ticketTypes[type].disabledMessage = customMessage;
                            }
                        });

                        if (saveConfig(config)) {
                            const embed = new EmbedBuilder()
                                .setColor('#e74c3c')
                                .setTitle('🔒 All Ticket Types Disabled')
                                .setDescription('Users can no longer create any tickets.')
                                .setTimestamp();

                            if (customMessage) {
                                embed.addFields({
                                    name: 'Disabled Message',
                                    value: customMessage,
                                    inline: false
                                });
                            }

                            await interaction.reply({ embeds: [embed], ephemeral: true });
                        } else {
                            await interaction.reply({
                                content: '❌ Failed to save configuration. Please try again.',
                                ephemeral: true
                            });
                        }
                    } else {
                        await interaction.reply({
                            content: '❌ Please specify a ticket type to disable.',
                            ephemeral: true
                        });
                    }
                } else {
                    if (!config.ticketTypes[ticketType]) {
                        return interaction.reply({
                            content: '❌ Invalid ticket type specified.',
                            ephemeral: true
                        });
                    }

                    config.ticketTypes[ticketType].enabled = false;
                    if (customMessage) {
                        config.ticketTypes[ticketType].disabledMessage = customMessage;
                    }

                    if (saveConfig(config)) {
                        const embed = new EmbedBuilder()
                            .setColor('#e74c3c')
                            .setTitle(`🔒 ${typeNames[ticketType]} Disabled`)
                            .setDescription(`Users can no longer create ${typeNames[ticketType].toLowerCase()} tickets.`)
                            .addFields({
                                name: 'Disabled Message',
                                value: config.ticketTypes[ticketType].disabledMessage,
                                inline: false
                            })
                            .setTimestamp();

                        await interaction.reply({ embeds: [embed], ephemeral: true });
                    } else {
                        await interaction.reply({
                            content: '❌ Failed to save configuration. Please try again.',
                            ephemeral: true
                        });
                    }
                }
                break;

            case 'status':
                if (ticketType && ticketType !== 'all') {
                    if (!config.ticketTypes[ticketType]) {
                        return interaction.reply({
                            content: '❌ Invalid ticket type specified.',
                            ephemeral: true
                        });
                    }

                    const typeConfig = config.ticketTypes[ticketType];
                    const statusEmbed = new EmbedBuilder()
                        .setColor(typeConfig.enabled ? '#2ecc71' : '#e74c3c')
                        .setTitle(`📊 ${typeNames[ticketType]} Status`)
                        .addFields(
                            {
                                name: 'Status',
                                value: typeConfig.enabled ? '✅ Enabled' : '🔒 Disabled',
                                inline: true
                            },
                            {
                                name: 'Disabled Message',
                                value: typeConfig.disabledMessage,
                                inline: false
                            }
                        )
                        .setTimestamp();

                    await interaction.reply({ embeds: [statusEmbed], ephemeral: true });
                } else {
                    // Show status for all ticket types
                    const embed = new EmbedBuilder()
                        .setColor('#3498db')
                        .setTitle('📊 All Ticket Types Status')
                        .setTimestamp();

                    Object.entries(config.ticketTypes).forEach(([type, typeConfig]) => {
                        embed.addFields({
                            name: `${typeConfig.enabled ? '✅' : '🔒'} ${typeNames[type]}`,
                            value: typeConfig.enabled ? 'Enabled' : 'Disabled',
                            inline: true
                        });
                    });

                    await interaction.reply({ embeds: [embed], ephemeral: true });
                }
                break;

            case 'list':
                const listEmbed = new EmbedBuilder()
                    .setColor('#3498db')
                    .setTitle('📋 Ticket Types Overview')
                    .setDescription('Current status of all ticket types:')
                    .setTimestamp();

                Object.entries(config.ticketTypes).forEach(([type, typeConfig]) => {
                    listEmbed.addFields({
                        name: `${typeConfig.enabled ? '✅' : '🔒'} ${typeNames[type]}`,
                        value: `**Status:** ${typeConfig.enabled ? 'Enabled' : 'Disabled'}\n**Message:** ${typeConfig.disabledMessage.substring(0, 100)}${typeConfig.disabledMessage.length > 100 ? '...' : ''}`,
                        inline: false
                    });
                });

                await interaction.reply({ embeds: [listEmbed], ephemeral: true });
                break;
        }
    }
};

// Export functions for use in other files
module.exports.loadConfig = loadConfig;
module.exports.saveConfig = saveConfig;
