{"_id": "@jest/expect-utils", "_rev": "70-12e31e41aa3aa36da6ce6afdb1cb2bd5", "name": "@jest/expect-utils", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.3"}, "versions": {"28.0.0-alpha.0": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a7f8d4e48011aa4a59168f8d93284f2f9b568d36", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-2Wcr4Vkr/Vj5KJylG5qdE+F0P7Y8qEhNKW3S/nsk+xa4ELqlWGRIJLR76q8VIAD9Dir6TTrmELp9uAwc/JdZFw==", "signatures": [{"sig": "MEYCIQDrTfv+VcIE+KoCnXLU00pCex8R+f9IBqQIz/fBIEad9wIhAPxhHjPr0QYjqAT6Y3HfskSVHE2xl1bGAPjPQVlBtSJu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa6CRA9TVsSAnZWagAA5iYP+wcVb1VSMv6J2Oaur4Va\ngt+6sFXAMdRsJ1txtuyUDvk7YpxHE6ls3nxgX1edBYAFYh/lrt4k6vrE5IpK\nh6CgkBFgRLTcrSUxkK9kSMP9hPH2yxLEDXFJykxL0BbTzBjU3HHNBPqdA/3O\nndNuoxMFP6LA3d8u5WuNeKIWcCUbDilBn3zaIIwVNc8eYCl1zhqJRCFIHgcS\nW95lZ1nivmYf0tMJLyqJuCt/fbTHdo6X7lXmFe8p17MpvkAWozip6p0NAuWz\n35rcC5QzVkjmJBNUsvbqkJve/y53B71B4Kx6oAVhin8nX7XxhwSfpN7i5MSN\nMrj34VqIXC10jcCmE3ZPS0WZ1sWubL6461p5ztGcvtWWfcWCbIVlrn269JLj\njlSVX1eRXb5F4ZvwKGaoCTZhczx1l3jqRwDKTf44mqwtU0uWG4rhfJ3ajdlm\nOxHk1cnmTfdummZh8mzkykaKXbj/KyResp3GIP2W0/NSZZaCfESexd+M6pAK\ni77eLBPJSSvp29RceCLnJVWFPP2De5Lf2ZBiCvNO6r9FlQLn9WIuT/j/Uhh2\nsc2p0i39omOxqkeZcMBDcHKDT3MR9ZGC73hapdVYIO2cjlVEqdjk77460R7Y\nQn9vr0e8kgFy5dXIPWzUzmnR1SUZJSxgaPLTSgTtX5s1KhIBwHKZ+pCbioea\ndO0e\r\n=JwOI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.0_1644517050537_0.19230723164528318", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4ade48e35ecd3d01eec4e5277fb6f37816761dad", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-b9zSTU4m2G1jgM+ekU1+yVHNAYMOmE7cVtJGKr3g/QfqVVwtE5hRdvgGXi5EbI+wbKnUetub6tVutZbQfKrxrA==", "signatures": [{"sig": "MEYCIQCpILrBdtVfQsRj9SC9kJEQ7wfvRpx479fAS42SUHqnVQIhAM4xYrR6+Du3z4As0YrNly8o9bFpIi1IBJvG4os4cJ8V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqZCRA9TVsSAnZWagAABrYP+gP1NBMnbVUKCVoy+aWF\n0xffHYQVW9T55deIGO3Vj/RvbuQPbkBdiK7pViQHkL0bVZOPlVvCmt934u2U\nvwHLmOJpzXaAG4GSTerDNsJRGM2L9C8qjGRq1o+1YYBZZ8eIpMdT1Qb9qMcj\nKwBaMiJJPHGTN1T5lRf21tL4cnYOKyozumNcxL+JlmWHyYc1gfzo/YlFxdIw\nQr/o0/z4oWcCrDdaqMHKtuNU+5hoWYkEmq6+twYgajyfH/6avujUAcP6rh6U\ntdMCC4zUJgrDgfm5pneqnQXCG3CwwxH6K+CoJPKZKIzRKx0UD41zv2Kz1HFR\nGjVLF8vCNVPzBGPWd6EYfi3oJzS88wGobuwmRIzDDhoSvwn7OhUcphwFF2Kc\n7I0Ydg4BDPQC2edaLyWLoxITaSltETIZKvZvmZBG+jQSeZjhoICTgHCypZ0l\nB6ZuNnjN+bZKSun3UMh2Bxq6Uj3A6MQcqCJvUnFLzzK7aaSn1Ae5GhNTMZVi\n3HInVLccVKdLPPiBn4JMo9Ce15xP73ZVWP7g7ABNzhuAlHXrCGMoONn09kSQ\nAh0+Lj6nmRfzl7crbfCzrmwhIAxpkbYIPNXpJNvLv2NhmT4NNRQ/MR9+26Um\ntkmeAGdYZ4mpqUMvEVkOYCZgDSsYe2eTvqBj+HGcQEDt+ZL9WbLhLoavO0y7\nDuiP\r\n=5Rw+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.1_1644960408952_0.9635472117761343", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ca891e797d7c05cf2f70da0e77e8ba0c390d5dc4", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-DEKs4FGKL+Qf3hU2CUzHI1f9gsgngmJt7oGHLDLo8gvgdok2MqHQPG+n0zMmq7OAaoW3hTQTBPWW4Ug/zxjXOQ==", "signatures": [{"sig": "MEYCIQCc6X701es0wQ6yT93e73U+XSejt6NfUvbmJMPcqNYHHQIhAKWh41tylTkrgO+wNO6xjDrwgnNLPZpdnC9ta4uUBeWq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5tCRA9TVsSAnZWagAAQWwP/3hZkwOOvc5qlu5CPMfJ\n+tir6gbzQUXt1eFua/Bzk7f0317uwCAdZAmo/JYSvktilr5iIx+4TWFxqST4\nWJuTVl7CCyvyU12nqX9kmgyQkDG5S9LBoRhGx3egK/O15ySJsZLnFPrtfIeb\nE8qMtvvooZA9oOnxItK4o/t1Bl9o/SPAAiVksfbLDex+SI1zrGAO0vjI0LTM\n73l+CiIcTeOLLBkI7jL0jdhFgaMf44OmiSvNbibBroLs8UuP1cb3lxuymVK9\nUFDRoVsRwj0Tgud3relpCkATjerMQkH8cMXm5ETA3DDsoqHuik2x+H3wM1+E\nEogq1/ZCgn0GEiElTUhV6353r6jMgqFAcxU1vVJLq+T5HAJ0rXeRwABA4rS9\nYYmf5T1kI16LWWEeoGrpVPxm7anMeKUTKsMR0ZnGwaVGEwy/+27wqVR3R7W0\nqDJPjHQUxk0xkT92FAjY0nT9kezb3YHKCpMDy8OFrEDPRiawpSWbR2rCNn1w\n7Ru7umP2uq4FKMBV/BtCracD5l2mLCUvEES6HI7ErQAwTU0E9vOphaUhsUQN\nR/VtH6TinhaRiBS0Qm3EYOs4SBSiaf0Uqc8EgmAOZM8Qx8FEY0hfMNczYZOK\nLG/Flp/Ja67Xv7OdckE+o6+wCCDViFG5jMqixFm0qpAkJdSIUN0Sp6IxqhC7\nS5vB\r\n=1W7P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.2_1645035116824_0.7978368079039273", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0ed65e39efeb5c8f6fb0a0e2255a57bb7fd2f149", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-riTPBZU8sP+AwTdS54BswUqcFUzbfE+X+7fEbWk1RYtM8j2eaoRHXwdnuTdNw5Z08b9sCoRSxGuQu/TLXuMJdQ==", "signatures": [{"sig": "MEQCIC7B458eNL+apg1UimSv9blTnDVYtrkkAQYRJa6QD1KAAiBD7vFMSl0BgOUqIUps0FyhQ6nt5uo1Uqv/IFGqTvi/zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjcA/+PuutzKoO7Dct6aT0NkP+hckhq1pZtxJSxDrSp0wbwONCvgIK\r\ncmRiBibmDSldgcRkRDYioCOonXoYaOhzQiRUT4Nf3qjdHLFH29W3hVGbFQWT\r\nYhOq3K2RJYzLdAwgCxeroVlCOgxVEH5dpLKWMquyQTqy3+Lc9u52UrIz1S7c\r\nPlbxTxHD5S7NWB0WNfMsc2uGTAkeIjnM9kHHYTesRe+VfKGDeSSD/JBaFtB7\r\nfiR5ofyyuWtrXwK0JZoHOb6HLJYHst7Yx44o86k+RPEF95D6Q2AjLrKfhDSs\r\nqoP9SxHpPN5F0d39FnVHcS8rfdy6uZ+VpJ4Swd+/0BfSQ4let+kb0Xkdp2PY\r\nxCS/JTCAmE0vse8QiexIeYg482q+TfwPQiDG6kEzBnMY8+UKowzCmKXtYPVO\r\nmJJYOkgXhdQVru94lUYwmpQ6BSFATrZ80HKw9iqxijYs4onepZyTZf3fJyOF\r\n0Xviz55izMrqDufH0b/kYsEnQkGavHTTyZMSFqku4piDfu3KEDTnna+CkMGS\r\nvUVFk7fP+xHptsQubzZNjpxyL8VItLHzPF55L5TM9yXVdW6qV0HcuI+jbnJR\r\naUYrSEN3XhOSXWZW07fxXqWCSYlbVyUPYWrOQ8Y6miAF1y20n0/KH0+4Y2D1\r\n2BuK4ztrZVUdZiIfvLYpvChyTWEq4+RoilI=\r\n=8O0d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.3_1645112541308_0.7379348188719019", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dc4ce9f1c15edb90a4bf9fe055279353870199ed", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.4.tgz", "fileCount": 8, "integrity": "sha512-G/eiFO6pRGgB9KkNoupI+fGeWp/FCGYK4D7RNmDKRmpMvBpIkJLZfX3zJQRmHL73nN5PvE9FQ2raZRrGt5dasQ==", "signatures": [{"sig": "MEUCIQCjV4ZTlyWYgDJyYm9PfmA3KYhZhNQmAmIFw8TI/BBQ6wIgZvDGAvujbeYoxulzTEiHLfEdGRTy6t/o08Hn2TBqJ1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJFg//fVgpWNf03i1etUgVTbD7lN2hpKXi89fcGBLgWhVlURZjzBDj\r\nlsOIEg1TbJzweinRBpBHA0+nGE3AcbjxcLGUF2Z1WvsGBfXbDL44CrBXf0N+\r\n5yizLlV/lzTCere7VSeCussMub4CgSHbHgJ7ORZTb0QbA7L2aaqqH8oT/w8V\r\nK30TTelAIi4ifzlWdiR0t1sdJMTY5cgsrUvACdikTLESPxNIbG8N8FwM716J\r\nYXSIPs2bexEjAUtgrZGUcvnXWU8o/NNEmT695+AAc0Nsq3Z2djNiiPcdR/1/\r\n9ljewOiVQ2bGM53RQtu1TWAGIaz9n82AQqBfO1Wcyh9n0deTMEkkJ8aETKNz\r\nYK3EdCg3K3vuM6N9W0yH1+wDD6vTea+9JD0cul+D/On8XFJMIBkHQO8xYXeS\r\nUxj9lxsaHg2XYrqUEFGwZVwSJ/jVf7UH16jLgljNl9j9hKWcqKaEFFcDOUNo\r\nQLi64HmlHds1TX4saaLnVRJJvyDqZcK54JjLvEFrG6WYqiAKuWjc3xkc1ilf\r\n7xtD/q3XWlXrwx4MVBEs4kLgb2nn+bfhK77CKyH2QoqgsfQZVnjnuhyLXeEe\r\nwWqghCYIkw4w+el9gyP9/62m8Ha2yLM2Qn4e4kT22HcaOVlLVaW2F1zrv8QP\r\n/JTrhl/QFD+6ieAs+dG/0kb+qZx3HsD2r8I=\r\n=pIWp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.4_1645532034404_0.13400283778028244", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9e0ae80304a229bd62f6e01320305d8284fb6957", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.5.tgz", "fileCount": 8, "integrity": "sha512-WL7+Mc/mdzE+vSQ9q4GYpQkhOShscw+kMHDaP4jTpcVLl8qfg22u8wLClsUaxOnnV7Kh2N2khXpR2yLVEq2VCg==", "signatures": [{"sig": "MEQCIAlPxPdZi0fTM9+09wGWQy/CWsznTwgLe+xrVGtaTvbHAiBy55xRrjWMf8RdsxwLlo0Zw0aMnG1KZ574ferqA0bzvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOsQ//bsDEPw9WyWPGaxYnsGhDkoOcdVIrtfMkrKCi0f2Xrbvu6xHW\r\nHlnFNleY746WyQo1nO5e9Ch01pKHU3k4aMewT6Lyyf6G6niPqIjWkHgsgHrT\r\nrRiwQy+AUbPQQO0jykh5C3KL531zKrHFd76IAZWfZ4vY4bN4pdjxSkChOvsc\r\n2Rti6/bA22KSfOWCWH1bQV35i9iTrUWmaO4vvuNKlazCoGn4RDQt7OGs1HNR\r\n9jxf4bB9JnFd5+dudfkvIxx9tkabt5gqE3UgSA6R+j1AiR3TQgWSRNNhqqtE\r\n+A1mdscUvirhBnzeg4yXq2sCjRrSpcK3AlN9s26jWcSw14bbvNDwPb1KtZmJ\r\nftgsN3xiQYQWLR7MpLUjoXxksfP48owQplns2rehvg5NMtw0W38PK85K9oO4\r\nyelsoIDOJWDrv6fZWtdQP+Rvei6Se8Oi1JNHuoCBolvgQt0NOJBJaleadpAO\r\nc3CR+RD6xvEz3ER7ym0tom10B8a8lVwhBWW5+FV4IE+Nf6szCYXom5ZeI36K\r\nuysvX1OxMSNJadyd3tT6jXZf7KtmvE6343sTAA2sXXHfK5UEufCe94WrxYi+\r\npXCS/MCbax5jfzChDdZTq1jjOdqMrR3RcSwcePKM0XiIJKeaHTt/gUo56qZF\r\nMFdOrwX6WSZKy9xiH10U748Vp2KV26nnS2Y=\r\n=Oq8f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.5_1645736237179_0.749154687752726", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d2aab0423083e4db624b911a8d596c9905116d07", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.6.tgz", "fileCount": 8, "integrity": "sha512-Y4K7HTuHBb4Yzq6KLQvMtr903mK0WVmyIeScugfahRjyrpr9OA9oUnMtQwufOQef+ufJigL5EwT3Q2KI7WzJcA==", "signatures": [{"sig": "MEUCIDgJjk1rKHffZN7b7aUzQ2QAxHu2iiE1YSSiVob/hFLOAiEA+pskUMCuXh7mP2XzJPoXSCw/dsHnI9cdl4QYYIA+M0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYiA//WPgVSDTfr38NLQPbhtjotpb7io6epR2aq2H0F0uy7TV9kJXG\r\nqG3tY9y/oXaIoxMPRKL4h6wXiUhApwf/euFTW0z/5lwhGi3IU2cTA0oeBggb\r\ns6A4t4GrXpDP3jS6nfKv7eg1baAZqrtYWsVT43tGTJIMdkpywdwa0+S3WyUR\r\nIE5vGXT39S4bxIBMWOf1UfaPQAbWA+23zpJMv41ne7VjFGQmwObRcn9+DzaE\r\nb4hNB5rzX4pZ9fKSyB7/Q8Zxk2jqST0y/MS10MEZfpC2nB4xHyIMabS5y9GX\r\n5tDW08LWs3u2RvbMfXZA6WzPBn8WyuuikQsLAb9qXB/3BVcrL0HT5AvILCY3\r\noK/ZuPiN0WCRBweNtYxnZn1kwegjPYW6upL8OxQvSVHb+c0uiTFd+nozRhNA\r\nMi3wLLkfWr1PMVXETFosBKC2Jifw1JGaeUYz0PwU0HQEs6iAwsJGtSgInZTP\r\nc4SrgESCrretlZkCS0MN8JOi3NgGUALfbYZBEEDVQT1c9BSIba9tK0iQYMAw\r\nsKhwWP6Gzc3HXX8Vgt6tew+GWpb9U4w5GcR2qtMOQ2qy9K7ntXBFFS7xBzNV\r\n/u0H9VE0qBMbCs2664kAC3s49BFV6wvcDMOWCh3tkqAhnFUih9360I/CTjir\r\nu9Rt4OOOykxetOfom0RUFMbcoyIw/s9jQqw=\r\n=/iWq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.6_1646123542323_0.07766184714095203", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe711e3e0d3b5f3b15c5f56362b060dc437defd6", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.7.tgz", "fileCount": 8, "integrity": "sha512-RKq0u0gooSUKYPkAFXfKk9976WKMPbdhQHHwYg8OSIgg07LawerF+XcqIj8ZK11JeM1oj2m2CKP0W9K8u5K+yA==", "signatures": [{"sig": "MEYCIQCFNjjlCeyMpwrnEjn5fBz/W/oqQvPLy2RRAtrAk0cPaQIhAIE5k/iwk8fIBthcmdtjxSKqYVUgrgvHjNZeN/SPpa9m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe8Q//U4Vrr+0woC3SdGYpWPCWGf7uS/UyC6/B27qAh+AN4/k6zpaU\r\naEhBdWk3Mpl+WeyMyTEKDQ3Chgr6TYjEWi+T4ksfkt+s42bkHFEhtq3SaMMU\r\nK+CgVh8pYCKUjuTnY7D24cQ6Hq2cJaOtlEMVP8+LvbgECvXFlXLqmdL8rY+J\r\nQEZlFr3nabgdfEjh21FrCYp+qFzCd2RAaJKJCU0CEHI6mkytlDaHWG1eKfcn\r\nvWYvXFvaN8pU8MLUyh8y7UPws5aEPgKQVJ3IszCmaWN0T0TYoc2oWDctWwmM\r\nVRhcH/1Zx+5PG0DPpJ5YnJJirq8bnWlbafBuu91/ja9C1EENBkKAOwubApU3\r\n3uROrG2PRi+KH0tNqx/x2XxeM7vsB1Bo0raq3fgvX6BO7Jz2yXL1TNPZf2VL\r\nUPktoNqMPuQ+C4/kHGMYsdKt/SmUAHj54oVUEt6qSElbJ9Lpg7fTj7TuxbSN\r\nqXBm+cJ00+830Z8+pKv1gfgHNh66n7Dbr/D11e1ckAaHbeRYrJLgSB50DUBU\r\nEWwOJ5rKGyPAUdg6kRdkdmFvnNW4cnKUlrCemKgSLEGBrWDo4diung+AvkKt\r\n22fuF4w4VbpxaO3h1BOG4IGZstMZvbLuPiuARU0T28BiwMPTrKOUQOUSF73b\r\ngOgQFbaKgmG7TXqIxIWQaBHi8oPNVNMs67g=\r\n=IR/9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.7_1646560959932_0.6350497130234194", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "36df54300ed1029a410a77e34b167cbb8dd89825", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.8.tgz", "fileCount": 8, "integrity": "sha512-ExlLTbwxruDcRGasj+ftq4L6uAaRQY/ZKW3RC6GHpgW7wg8MfBwjxkAfB5Tyjel7net8OJW592qKJ2TNHXt62g==", "signatures": [{"sig": "MEQCIEXNsjYR69iasE7mktJyVEZ9SuhsMsf2bzJBWS7AL2bQAiBXhMqaMe+9azsW0320dJ7j3LNItwQpgKJxAKdb16ryBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriIQ/9HmBqdc+bKgotfHNFDXF+vXS81nfO4SNOJr38Mfx4qHS3Ok6O\r\nc1GDmuufMjzeXRIzxFUZEh9pEBTih0aBNQfJ1T59rL27ODY8X6ZNY8bE6lT9\r\nKBBVz+gJ/r/Zm7IcJUsnoOUUOyZaiU9OmijHxAZYa7CQEoPUEgvxfVHqJwFi\r\nhyJThrgfkIbkYLQhD4Cxcc8UHv0dBmNjXqWQOqLtXP+t90zobO0PbX0V0uQ+\r\nWxWWHwD3hpiDLxgbOdpKiUx9MV8+xhldXTBFbbfeVESHCps0n6mkgpkJK5/C\r\nzwUGmv82pLE/kZfWYON2gnDCv81UZnOPJJwXBzGn6tXjZYlnGB4f/LLLJwC4\r\nbMaACFrwUVhHWjghrAg75BZTi/MhV4KnOHZirF9Gq0dLmH+gRRHU879m6ps8\r\nBQwEpuXE1w8zTetRrdFfxh1W1OpDAg207yMagor5NXF69bjCF3Q77vXgJkvy\r\nczWUZjHAwlxxC2VJUJPQejG/sEL9Kp2yKKUeZaRQueUBMF76YAHOXoIDYd6l\r\nyNpm7Z+bItGTySP+Ajo1SHIoY1rtNroqRS6LeBDeC6NieCFufaHM9nrbijl1\r\nZJzpaGPMnRhAQAgaa9GCj57Lf2GJcsIShlTGwVPZGsnYOJhzMP2DA7JQtqUQ\r\nam3yWIZXVWvH/5sdU+SZqgk+j8x5rKhfH4c=\r\n=3Ris\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.8_1649170778985_0.8977605416682708", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/expect-utils", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/expect-utils@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "81fefe8e0523cc0cac56658ba019ecac4fd17bfb", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-L9eqYZnyA8lHLBlqyVkc/k8X3koi73wcUg6Abz7KO7S6IxB2is+qQP8rcPyoxN0cyqjryRnmCZ3pLqiDuuwj7g==", "signatures": [{"sig": "MEUCIQCWf/JeMJV7ZDx9Ai+nzt1S+d6V4OhgkguCIda6wnAlIAIgfDRjZ0NSTmsRbjN4KmzzlKB1UQLSYxcr+9IvFSl1dzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoM9g/+Kt0PlwNnN1NQi+x+F6eSx7LF5N5i9KAaicROCgGM3bWmlSZj\r\nNPU3rVMmKuMVM/oA4zEo86gdSXou8IQe9lhoUOp9IBqki3k9htuQB18f9Of6\r\nHVhxw7NYWUSY4snY9LMBPJCJuM2ENYbUxKsFKtSGzzhB+CVOX9xvt8rWGmkq\r\nf6+SbP+6Inz1m6y0ARC1GrD5FEbb5rQ9cg8pLLEwbJQGAsTPH05+YQUGsqyl\r\nNqRiiD7wk0Y1YphTcTwAOFKOY22oqt1pEfxohqrx5W9FxVR0Yq3L0ZvjlXKO\r\nqNXOHPAdykaz70vnswAxd6Aem0D/hjZzKIdqMR6fq96xPWMgv40MRRgdq64Y\r\nRcW1fZDBTB4a9BRx/CKCgWXAxjEPiN4vJkZeFZd1WgXyD04cJts+nHs3o9p/\r\nJovMvblJ+nntoTqHbxuRoDT4FxK1tnXrVBMSW52SpPL0/AND30l8KFjqILjO\r\nhJViBp03zTbhd0f6ktHxPGW1i2zJzR7SqZnnH5R06Q0WHxN/gcVNV+JIHVff\r\n1s95g2OfEO51eD6D3sLah11VCYi0HOACmobtdg81a6awkdOn1ISjtOIqSmPg\r\nLoBVyQYWvHQoNK+A0S8kIVgImxKEbvsBKokoT9n4bgMEDyq/5RB3RYcrkYeI\r\nO/6as2y1MAzsnMz13Zh7WmGC7C9QaagF+8o=\r\n=YsPP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest-matcher-utils": "^28.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0-alpha.9_1650365953799_0.5946555865019822", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/expect-utils", "version": "28.0.0", "license": "MIT", "_id": "@jest/expect-utils@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a074371968c80ec71af3053f5083d8e9a7529c39", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.0.tgz", "fileCount": 8, "integrity": "sha512-C/lpxRTC60wl2TIwttFm/qoccZe56kpE8MVDZUJjtinxAOuPFAolGgcon3qs2ggOL2+9zsTSbt648rB4D8RGdQ==", "signatures": [{"sig": "MEUCIQCSy3xbR98IyDnHrFDtXR/KCQ5Si3XmYFEGhnkafIrSqQIgXqL4hbcjTXEGLcdPExEUYmN3ccfnaeE143H2ZZcvCEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozCw/7B+NRy9lWvEhDb+uQFCvhpkgSxuO9gNBa9vDRQpyEusLLexK6\r\nvIi9C8lP+wJghwkJwcP+SWTWTBWJcZwCuh08//PyhO2YJfQe2Ur9KpKGhspL\r\nhRs6eWhvHhuS4Qh5bC5thEOBpwX3mKo/AK2kVbRrXfWrfkrNjjNKciYVLE1w\r\n2u/vQ1S61201GOKjlP1u+3O4tPwebrmtx+9+RmkQvwTY1cqu+UG65cHWBmek\r\nzvntWdobTcskzUNb9AdZavT6Kt9bz9aJS16UE6CDtPqSCdpfe292u19CI7x3\r\n3UYvVL8HPsBh5x5LlRGpeA9lFLvaRJEvTTMQose63x7wh08ZvngyTzPYmCgf\r\nfC7JSPUh3hD6/oQUd9/RPxZv4+kJTWtiy9TF2WqkIEgOEL1msLpZUKW4TxEn\r\n0FEFj0RwnAjFcqF5BIGs6pOu2cHeGmp/SsWhnMvLSfkq4CSkLiKtzHVhdYJP\r\nG1myO0ywEYhSW1POEoHTrvz5J/lOb1H8Wz7k1UZoc8IhLlVTWMDsDBPByjxm\r\ng0za+ENodUzYCjHL10yZUc76uVEe7c+ujtUyW2RKXgVaNDoiGhZu6kMsaVqP\r\nGKyZZSRSiYIJHTTXcWSZmT33/LYp7+t19R7OeMDBrsS1G+OyT1VLa80DMhXz\r\nt+j9AwvLtXFJxMCzhh1E6mgZzIhOXZQJD+E=\r\n=LC6p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-matcher-utils": "^28.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.0_1650888486547_0.5639144424116589", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/expect-utils", "version": "28.0.1", "license": "MIT", "_id": "@jest/expect-utils@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c6aa0fb629e3d205384406c2e6cb00b7916c597a", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.1.tgz", "fileCount": 8, "integrity": "sha512-ctuvt7SeoVlG3P2eemtq3/TF5a7ncnpC18Ctv1BjCfBjkjVKtAkDblw6qhx24tZlYdhm0lrihwK80pkzmkUctw==", "signatures": [{"sig": "MEUCIQCeRmu5Bs7AiSJgawnvV17aOC99buZHmZBc0VRawkSn2QIgcCB/+/CudsfbiPcr/n4LcropQ/n42n1Rd2MBwoBPj2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8M4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosiBAAlOMmw3ela3A9KxGMnfwjGI2Rb8kdPIXBkTxCRGVYhSlXSMi/\r\nQYy484tcg9UB7QIfHgMdL2BNEv7aIBuA9DLL9nCOnspFxPtuq+xa14DLrGuc\r\nr39E3LLaiWCeyk3/7kpFVuGPB7Ss7LIfETsTEUZ2RaMrTRzNm22vrUnMTdvW\r\n89ia9F7NMFGS7zjpSd5xG9W0K/8fy2I7w5TCDTpvvs6Db0HRxCz9H4A3ruiW\r\nY0l81TNW+J61IaK6nwmeRNhN4QNnuaKQRCGoZy89OgYxOqfiRjSYoSvJi0x0\r\nnFPoJZySRuDOHK9pPtHfwjZjan7ZEmRCXPcl96wdDVLwmDeJrDNWB1ytyeyT\r\nZ4F7lk6i+1j2YeZy5qGG3uuArV1eozheATjSnMUiW7WhWbSM/K3V4LVTKwxe\r\nX4z1EymBabeEQ4kLlg11EEm81qS1j2NTtFlB0ckZc2toIugr/svu3Ju8EpH/\r\nfy1dHs5zG4NRJQL5VR68xCT5MAva3w68R3CRtQPvwPybFBD7UvtptdW2f53t\r\nvZ5fxF+ine8xd5hKfzT3tzYtQiQ5g0Prjsn/I3OlYUeonejnWHVcbz+xOZTY\r\nxVOeLma4yJ7H/bV1jbbOZrm3RmyrgnzqLjAYKww5Y400IYvYZSlnIZUIXZ4a\r\n+TKzacTjIPrxaDqQ5PxIt76XiNdpymEdgXA=\r\n=DDUR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-matcher-utils": "^28.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.1_1650967352199_0.9243547418996518", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/expect-utils", "version": "28.0.2", "license": "MIT", "_id": "@jest/expect-utils@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0a055868d225261eac82a12013e2e0735238774d", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.0.2.tgz", "fileCount": 8, "integrity": "sha512-YryfH2zN5c7M8eLtn9oTBRj1sfD+X4cHNXJnTejqCveOS33wADEZUxJ7de5++lRvByNpRpfAnc8zTK7yrUJqgA==", "signatures": [{"sig": "MEUCIQCwk96WSir2lsNQZ0ISfu5Rro9ExLshlUawowWQMaNsGwIgTjg4OIWWfiN36B+/yzVIY2Cn4wsyvkoJ2Hc3ieFnT7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqhnw/6Aw8Ec67zTwK/3cm6QyVvkx0tsRTIYjEqDelJLoH1ShDQBtUq\r\nOH7mm0hgjUOFHCUAgZwh7Ud4mkLNsDhoZ0FE/6fCd/7B21JDzFYEQq24xieb\r\nU+WCpzMLPaop9CG2AJ60+eg9uSdx8qc2FHH4YFmB7B8dc0RJbpJne6lljmXY\r\nfWbPJi1SAE298JzXoIUe58f1dlHA3hWqG7z9/t2U+ZvZ3h6xmuiCts2Aukh8\r\nDNsPrvUigGXyi8XxNT60tCKnWu+zd0xx+j8AuKYRJhXz/gmDjDI25miKksUZ\r\ndCTD9eeX9JV4VR4hz7UCHdUdXfCq6AM7Mr9Oc0/bq6IkTZpCXYpaBHlGE0qC\r\nm0m6CpWH6TOukSR3uVo/PPNQIr0H+DRW51v6fDpob0occyvGkxBDupmfB/Pq\r\n8CiqgSFwTnSTdEiEm0UQweHQtDJkpplweUFNCCiI3ISwDc/XNV/DWGwjvMU7\r\nJ/MV2svAKZiOnoI9J68iB2fsF/PKC501/BemeXNYOAiIvLD8yfoo+dDBLm2F\r\nCl0VqUfA7OgBFGeB8Qs9Xnudwl1HrzrkaBCx6ggrQtf8UNC99u+VZa5znVLO\r\nEWvX9D/iUr/TZOijZ+6BuL7N5DlHtB671JeWTBJBMozstSacskjdGLbEi9QS\r\nEvj05tpWrRKjcr5FPP+ZFpI0rgdn+s1iZXk=\r\n=ozwz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-get-type": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest-matcher-utils": "^28.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.0.2_1651045441878_0.11884923266074487", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/expect-utils", "version": "28.1.0", "license": "MIT", "_id": "@jest/expect-utils@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a5cde811195515a9809b96748ae8bcc331a3538a", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.1.0.tgz", "fileCount": 8, "integrity": "sha512-5BrG48dpC0sB80wpeIX5FU6kolDJI4K0n5BM9a5V38MGx0pyRvUBSS0u2aNTdDzmOrCjhOg8pGs6a20ivYkdmw==", "signatures": [{"sig": "MEQCIDeT9plI5sS+NMNd8oPE9p6VLknAjoqoxPmnoxP6LmpmAiAHoOZT44WB1PBtG/u3N2fln83jpbtAJNEQuxTcdfT4HA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOfQ/+Ozhgz80tV74vHk/GpnLH1yubyKioJa0fcbHuWm191wScQBeC\r\npnIWCj8UQGYKpx28+xV93SNZBEyya9DaXV3G49j2Nf9Oxqw1vP5NlCGy2kIl\r\nnQTQtNYK6Npevzhq9BHM87eb/6GNtgTYkq4iFMQZlnyDeuWX2dYoShoCOTS5\r\nE6HzVioJb5D6nUM1TSyfLKTvIISu1GZUchGBmJ8pLIADuLVskbcaffZvuvRK\r\n6k9qB385Thm03unL1jHGeSKrFwyz8sjUl8fkk831c9co4C66SJpCwbmJHB5x\r\ndKZt5vvKUJP6HdznhX5qdH1x7RvZSjDB/kKcvpbiEhNa103ME9Htk8wg252S\r\nIP6CoyFcpe7TuvwyrQqtUYEjEtI/IYwTMABLYgdUEWv9LfJWwL5tnBF9sP7s\r\naG0ruYe3pzlh47BFFyLDISHuBjCu7SVIyhyoav7wCS4OBjeHD7WPRSU10+U0\r\n4pl1ejy3pkROfRB6XS+GbeGcgwbWVMJcmm3l28XuNan9Q9h5v8rOgeNAgTM4\r\nOAeHavc+DHTC6SVpG88kAhFmPZ8pX2eS7a78ZyeRvKgth98uodMoJfi6Wmdi\r\nh7DUILJFa1Zdy6jJNILYDjwkzfUz8RPASXGhMKmkOFRC52gVJ0KjXHkP5WE2\r\nQ3jyd0B9yBaN1LQoI0FkNhvL+oT4PW0Bwqo=\r\n=FUzU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-get-type": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^28.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.1.0_1651834133102_0.6931225468553728", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/expect-utils", "version": "28.1.1", "license": "MIT", "_id": "@jest/expect-utils@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d84c346025b9f6f3886d02c48a6177e2b0360587", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.1.1.tgz", "fileCount": 8, "integrity": "sha512-n/ghlvdhCdMI/hTcnn4qV57kQuV9OTsZzH1TTCVARANKhl6hXJqLKUkwX69ftMGpsbpt96SsDD8n8LD2d9+FRw==", "signatures": [{"sig": "MEQCIDbW5iiA+TjC9YLbL9EfcAjc/hXWGjRHmsMfrDaa9z9sAiB96nI17DRltIiX2NupJEEzZ0ZmfYa/QIwwGHe6I3VSAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuufACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHxRAAjtSL65E5LILh0M6EpVtAji6kGcpo98OI8Cg/duzucMl1mqzL\r\n2H4NAq26+Rf3Q3dWw/rSk5PdXFkGKls84zgFliASIxesznoZrPa0lxh6d1Om\r\nIvK7a8G6ase9bq9moMGLxywsP6ymApc+EHu8QjUuwxaojP0YIkU6rJZwKscd\r\nb36Fb1lueBMvho5ubMWl45/BqffoY/If4hmoJdgy/GnfArKxyM4hd59H9diG\r\nCUaPRLFYSYBtuW7YURORJYh8UOehpKVpVTsab77O7IkhBwRuTPSeMq4JZ0OC\r\nhqthQAupf/DJe6ER7aK22+LDupmoxdXRHjNYD3VsBso8qzq9lSaDMjmQgjtT\r\nCHLtbxwf7MBKzg9gBLgJA4Uv958UP8dhZ8Qji2Jzh2nIoD3sdwA1Mmui3k5c\r\nc5pegYiiYTg91J+qsoheVHpEak0PZooaXhys/T0QYlg35atANJqBgpdXwkFP\r\nlLfKYOobWDrk1SIxn+Ph8tnmrcqAdQ1Rv7LO2U7zjUBT8giYJixzRnUD/8Fn\r\noqiIkRSMJm1zl1RB+mbme3P0tu0AKQRw0z/M//idSlMuFADVJ3Zbmd7eaALP\r\ntpyocxWVhLiM3TG87eYXGpAd/NnCy8zYDcq+VVP2tS4bplY/dBOJ3JM+LUTt\r\n97pvO08sbfYviSWGTLhXhl8W9zFGDePs3wc=\r\n=Hk8b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^28.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.1.1_1654582175313_0.05243617756973018", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/expect-utils", "version": "28.1.3", "license": "MIT", "_id": "@jest/expect-utils@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "58561ce5db7cd253a7edddbc051fb39dda50f525", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.1.3.tgz", "fileCount": 8, "integrity": "sha512-wvbi9LUrHJLn3NlDW6wF2hvIMtd4JUl2QNVrjq+IBSHirgfrR3o9RnVtxzdEGO2n9JyIWwHnLfby5KzqBGg2YA==", "signatures": [{"sig": "MEUCIQDwlvJ9XSgj+M2qtVcem4jnI0eg5oUImzgzZnjJ1M18xgIgBsjVdn2Nu+bVPLRGQbz5VQ5C+svgoNZ8A9JWwPly/4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoC6w/+O2GuJ8kiYYKB6qwWLubKREtrksVzAo//xmFIXtD4TRYD0a4P\r\n+NNm98Hlqk29kfIBjCON1jm3wsgCqWyQyknguX36Az9V+IfVTuUVtnAtWSbX\r\nU4YK1zsExcuSNH2mQOBix0elguK1HwDuCtQX4OL2TNNkyizU0nDnhHYmt1i2\r\nZ+dXwKkyNnn2Wf9bbkH0cZDqdqTN5E2wlneTD+HRa8sCUhsGhyvzhC3maf6M\r\nuHcACJL3km5etpEYBx4KKso94Iqx3OziWeDuw58VeUMUGkCJLaMateoRZxcy\r\ny6rTF8GuHwDVYmYMeAgp1xIW5q2nmfUHFkSszbNEwNuo5z2SnZTz9wJM42Vd\r\nn0fqFIowGcc6WImZVymAvzscQvrqfFvkj33uiOSRLl3RnOg61wE27ML6M/XL\r\nzMBexnQ4wpr543cs2lO3LTD4ZPZrK2zKO3N8ElkQX+NY7n19YyedMe/sw3Px\r\n3Gmd7g4DvS/y3BIv8wSgFd0P+UZ8uuQ7Smg4cRlirEzNituEhzR2sm/IojbD\r\nwqs1IboaELBcuf2O1B8Le5nkep77Exsx8BiV8lqSWB6gxJ6pwP+8gS2h4j9Y\r\nuHzjVYeNEcPzBj+RV0O81kT+3RvuBwe0YplxzH1n3TxOTIbTcqvvlfsA2JWj\r\nGgvKWKCSr5ArHr3nClEzQAsanZ1RDoPUa+w=\r\n=oPu8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^28.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_28.1.3_1657721545823_0.8704155562399212", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/expect-utils", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/expect-utils@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "df5b07d47dbadd8a0de89d26a765ee24429b710f", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-3ySOsVt8x0RmIdBRGAwEQ5655LIZUrcyvXcFKtQDiZFtHqP/ycOYU3VfT0uZYqF27rP2oPpVrK54+fnMoCOk1Q==", "signatures": [{"sig": "MEUCIDvjrRIwaxxBZBUb4cmw2RJeJfHgiW0USn3CRvnav3wRAiEA0MGnHsGWLudACjgPnwtXrDszZ5o/Pls9/0v8hJP1bjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXGg/+LxfYRqqNGJgN8XbubEQEZu6fEIODs09MiB/SJ7QSFkGjkzNQ\r\nfFEfgs6KbybiS/JVShtzSRRPJ4NGTO4SalIckjTbpH4rNm+mxKeSPYrcNh52\r\nMDvZraAqUb8x4iXiiZLi+r2pH3RoILS/lpHor00Iqm4avl0qRmBOlg/R1bfO\r\ncdyCkStCbUner2LDKkv3fP3V4cXwXuBn/5SpwadVOn+lqIAC6V23IF7iYYzD\r\nfwGa+RDPhtuyOXcUjxmmQKoNu5uh0CR60Q26FCjzK3YWHxraraWZIDrPiQ0T\r\n7h4my6CDeHW0PjLAaDaY6gwqUO+9qRY7evDpFCS1tHb6OyVIqb7xnRPgZjHm\r\nwMhinRg5SG9gCU5qAgjCSkyWy67FyakqycN7juT9hLRK59hICWOpcuhNh5nf\r\nyfpnI1zwqwMYMMuHRT1tHSjEdB+KVlFW9J9nNlVgvtF4CVbCFGX39PHEx8zh\r\n+aVGz1/V+glmYzP2E5Rox384J7YjFeV/yrdaY+m3oUIwZFQB2tK42u2VVHBn\r\n07vgaFetl591091yhfF4XTb+3D6z9qHmKtkoTBSQMmZ4CUofY5TeIcN5C1wf\r\nrq9aKy3iyalsneWBAPXR5txPcQHknSLNiY7G2WY1QLX3zW6HAMN2pzKvbS+G\r\n+1TrappdwEcE1UT3MlbS4DwZXuSN+PL3yp8=\r\n=icWS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.0-alpha.0_1658095626769_0.1455980880495813", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/expect-utils", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/expect-utils@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d435c3c1e9b44fb6c8941745dd3b67b7c4489c70", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-aSj9rbd0D2P6dZto9sLlmCqLbN3/yrYyn3ljUf/vUKBPWb+fGSRv3/PlBQurhlbuH9IF5p3G1kIUT40Y1BNBVw==", "signatures": [{"sig": "MEUCIQDlhRVtxbd8AkkpoPiTsio6QfzaDAYByVt795j45T/nJwIgPY5jAdodHKZ513feT64W79hYJ/ocIlwVZ1D2ZEAgOB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO3g//eiMtaPouBjVb58y2fgvWeVdM5dWiWBY++Ae48KGnm2BYbFAH\r\n36xsxUBvk4kSi7yPvpQUvvFH65ir7q+sumRGpYw4iB+UdT3e7CX/dDdKcWjd\r\nhA12F8QtJHAKG+3NrWvzmZY1ZtgDF4N/fKGMPqkldvCXA4FxUL2/38RV9yyi\r\ntFDpq7BdgFhwa6z5Ht7IKWeedN+wWRDC3+ryU6dPKxKKUZZSE0CFCVrzKHaV\r\ngygXqkm0s+Rav7EV0e0abPriMWNkDxNpNDLtUAkA2833HXmz01mW4CO6L60n\r\ncvzAOAbRUNRXhIi/s49/WbRcdGc1xwEk020UPT7q5xX12xsfXV1v3vyNOn89\r\nDKnGnMN4IKq6u0ju6tOnn6CNs/VwbJfaQX2gJ789T4bHofaA7bTjmqbMIob1\r\nwhQQwPIHCK7OLP+wBro7j2hfVARMwV44HobR9/2EoxnQl2lstysLU2evxJe+\r\nUgIuwt7xW63QqJLahhpJWIUP5rRqbHxaHHCPD5XXTO3DQ/JupOAZEdzHi9Ge\r\nH1WuxNgijM2n0M1T4N9O1GTQr00W1uP7MiSrw5ZTc+CWFge56Gx1QE3wmTWt\r\nLS9wYqIincQlnilyxBWKamxd67ymTsdlT6RYj2G8bwEBcDx+wacIv1iRsFDo\r\nxl0CublJK3Fz9yrZUGId6AubG66CrJQ/buE=\r\n=RIHS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.0-alpha.1_1659601408400_0.4084247128404277", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/expect-utils", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/expect-utils@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "924ab2a5c08073698f43841d6585ca604a1778ad", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-cN0OlrW5OLji57PXIZKB9JjljBB+SKgYCVwlTR2uYcZS/7PYH3yqWkFVXUg7XeOn5mH+vsYnYvHROpBNydYtOg==", "signatures": [{"sig": "MEUCIQCWpfcvoS3L8Vq3/syC5hKub13YEOPVxgHmxYYxzUoSUQIgVK+HENItMIsucNmapwhhQPozN3zBblrr2WGNZdMR+84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqqog//Tvv/4IFQaO0YJq4TsHOc6YQSjxfSQgC6SNdOCzA6UBspVm3h\r\ntHjfcBZWE0A58pwetnc2/QY2Ou6FwCzFvmMY33XkyGhUwkLtpwbdYie53IXv\r\nc4Bb/8UvQ2Yf5/D8FodVMgCk2QWSuc/V5uk3DBc2FpCPVDZFN+URXfxUuVXX\r\no6wbMxTahEtaswvrR9B3jB5HOZw7hVgStcwFHymhOpSdLAJ6N79BJbdcKi6Z\r\n3+Rr<PERSON>ivbTSlZ1KAIF8BwGJIEZmF9j2pEG1hpHAxM9N7W/GGvaHAauf1ezAno\r\ntTSbs2/OLBvUUPd3f7lCZXJRfQ2Ksu/OvPN3N2LbquXqk9vHxmlOqogQhZX/\r\n4wBPnztM0ABe4aLwkpzC/FTBsBY1nkyIpUNgUdasB1gLx3u2oXgjoTG740it\r\ndmnivx9/oRtuUZvIQ03KzRWQAgUz3S7HlaT3YfmAXkNfWx3aQR34qRXnbTWe\r\no+zdTJQ8JoS9wyqVi89dRnmAnrwLJAOZt9FZs1kSgu5od2QTTQ0Ee2JlYLDp\r\nr+2fP8KQmtDFwiLvqVK+LiTmf4kBUXZN1Hd+mO3ZX25LIi25lHGX6XcGxEe2\r\n+7ImmYXRBqgHohwt72dyadZlF/T5fSJUx+6Q9uu6RuDEUgdCpVRfqwcuwbZD\r\novrUHa7VMzbJINOx384sqazd+KQFev3ZiDc=\r\n=3sFb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.0-alpha.3_1659879696220_0.1173593981941441", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/expect-utils", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/expect-utils@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b45915a5ffc1ad6c18e15fab91eee6984084de24", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.0-alpha.4.tgz", "fileCount": 8, "integrity": "sha512-KUeHD+8w+Q9gP2XHwf1biOuCp22GRFOovs71HJRHe3LfCP+yITNbLPyJPPVq6U+a1R+kznNWGOkfd4wljT6RGA==", "signatures": [{"sig": "MEYCIQDeq3a/CW0TdFeitpkHEXT4GM62E+l2LPDpPMtVqajOTQIhAPLBSUZuLdUzdX6k5zyVXWaypXAaLPW0NEzAMyP+8I2K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QoeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpL2xAAjxjZUyz8eeg1+ysTp9iXHSqey32KRcO5A1dmsLSd/psyQ6Xl\r\nJDQl+bE1d+aL+3R5ZzYIsoqwuECfm2MZ3uRM2DB4JyqD+8ASKmPvoXSAzeBj\r\nYzdAWSKW6Jbwpk3cAeMMqrlgEgpNbNYOb7FHAv0fDwn2JIgsGJoaQXF7nkT1\r\n9JHN7WjeKzYSfWxnj2fbTMyrO36EZmOSrCbYV7MiPFbLn62hWbFdc+k+GSsu\r\ngsmI8yI9v3OtLTLpcVM/ZmPh4JotgoudvS5vwvetyyN83mng+1l+1E/VNRbf\r\nreiAr3z/BOuRqBfUBjNTnKihnVSIIMYHIUtVhmHk5cTX+vpwOU2suvwPx5wT\r\nWB7w/BKDgSBJPkc5GbA0sMoCxlrXyil8Z1sSd2i+ay5etAeDcB5+5hEjbF1r\r\nrvfQqkkL0zQF+y4kBOC/EWFTxzvZhPUqA7F265xbcHrduYQc+H1TzeA2zxKA\r\nJLLyWMjuOdAiDH431dHl0lA7mtuXC2WKFbtfMDjYO8OygRFjAJZlFmMiauSB\r\nuU+64G2Ab85MkOaPX8hVGjq+o3U2aLO/TeWkNkztDLvNcVZKxM4YBnk4Yf1z\r\nv5vJOgDL+0qtYSgyu50Hgmqk/H0HyNvJFAD64DaIM2uYUGIJTr19jTeruvsg\r\nXXnevQ8TVD8Kl0vSUM6nAJrcs/5TmNBrZmM=\r\n=178y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.0-alpha.4_1659963934721_0.9653050742513793", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/expect-utils", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/expect-utils@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41731c9a19e9ddeb6b76b3a199a943249ee5c19a", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.0-alpha.6.tgz", "fileCount": 11, "integrity": "sha512-0yrmEgx1UFv9EZD6qUjQPPWBPaX28ca4zoObkxLQjr5dL43UdYR42DMkeHR4l7n239m5Rc4txItNRUjPqLSOJQ==", "signatures": [{"sig": "MEUCIFu8YwETGMJYQ3k8Y+3/TnSxK0G4gH5fdWwwNtWLOwltAiEAtaIvhmPsEaLwt4sFOaKlIbk83g0x8zAWFN83ySdtVgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqkbw//U0d3vNiQUfn4ewJh2W2eScbqq2+NRHKpqWY9HsrdVUnPbOEB\r\nciGvSzMDYp19Zs+HS519brH3rC+1iYKGpnDPaMgVZAHMqfrh2XLbB1CgyXz5\r\ntTUKXBKezwAfJzwkTWAc2OfFFaGSoJ5AT1LB+UiSNN0xpLuVeJ3KOoM/ZgWK\r\ncnC6VJVg8ymsa/+wen6wmmw8iH6iYqp3L6+/pYH/zyi1B1LT12jXk4u5ZFEH\r\nr/Qm2lzqpQ1NHOAw7AZtmTgOkka073eiROto3TCD8dQQq4assBeQDqBmqUXh\r\niVsjNjXlV/L8nUOvBzrkYIw3+1uMCfJAUtn3OKxBODvdAlqq38uWz2hspQhc\r\noHVoiQAAYm2/1UFAPMUKhg3OpQJEO4sjYQ4var1HQLFf+8ExRzHpR3N7wGUu\r\n8svWvRi3khqnuPzz7Ck5wZ6v1/xv/35JVt6tUxguoUXWYwBHMYgoyqrP4D+W\r\nwAxI3xBgmIYxYRm1JpvgKEK67i2BWVLC1avdQf4Afk3gVOKmLndpRe7NQNDC\r\nNlPz54z6yfdlztra5aLjDAQLwnVOldX45+OfqdxOieBjGTzWf1hVD0XAqruy\r\n9GU44xjJ8VfAhubYKv2HbqcontaoNGFeYmOf6xmkuKddWAqKL7O5XRcLNFHO\r\nEkbj7My4xVGb4x9auW8g3B2EQ58PoXPOVj0=\r\n=VT+7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.0-alpha.6_1660917468725_0.6388455785784262", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/expect-utils", "version": "29.0.0", "license": "MIT", "_id": "@jest/expect-utils@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aa3a2cbe4630ac2095cd2bcd3040fe42015fe9ed", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.0.tgz", "fileCount": 11, "integrity": "sha512-odQ+cjUpui6++a9Ua/oWn7CG0Af+EZe9weWZbfUQHTg7C3K9PCb0AnD4X7nyAe4WjfeZmVVyG5SJELMQaUbCtg==", "signatures": [{"sig": "MEQCIGeLos0kOE/XIuRU1DhXJs4Fb4XqtueMgQOGYRk6tXU1AiBiT3e9jUm8WK+SqoItvTqWSczE989RNXG1FlIcozVGsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJPg//X+eziQFdFahv0qZJbZQUWETptr489JcCZwQkVCxCc23xmoBB\r\n2zfWf6VbFOD59jIbyiNnnAVDfxyHO5M3C+xELsBpQdhdz8PnAzVNyWJ6h4+N\r\n8DqlYOvvuJly2NpMk2immWPpGGlbYEyLsydt9uV/wGV4tHRlJBTLIs1oor5v\r\nqwrv7hnh6FzPVnMcgUXBjAAWKyG/x0UkVrliOHQFTIk0QcQnl21aUO9oV8sU\r\noBnU9sPCGEvG3AB1hrN07sKV0qSpQMOdxcUkIZ5CS1SFYEidSVZfP979Bu6v\r\n3QjejjYcOyB+4A0APRXOzAavX4h6JSOI4DBOvOFhUCv2POkQQPFo9edWf1jQ\r\neWKQCjrAbA6e8J9dNP8TRJQgcIeD8HlCxASeF5FmwbcG7eIGHR0DvA21ksTi\r\nvMnzC66sS8txsDK/3nX5r6bhgc/UPWoio/DBPlprzYsfpiN7oe9xMXLZYg0T\r\nycT0rC0w14IPpqmO24x5Pl523lHHNgL9vaJkfmMkMiniesY2+GefB9ZdoRlX\r\nUVXsz3rlqitT0/9a6fLm0A/K4cPx+UoHwem6m+wubeEfM3W+JbFlMBnWn1iD\r\n9hi+exRj9LHpwFLT+oPN4YyZInDCNmAcqmLhZqqq4xfgpBdDPu1QF7pCQaxT\r\nT41jHa3Sbqlvgxh4pDfLHNX0cI2XC7Gb8jg=\r\n=zGNH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.0_1661430808217_0.0845117455213813", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/expect-utils", "version": "29.0.1", "license": "MIT", "_id": "@jest/expect-utils@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1a84ee66caaef537f351dd82f7c63d559cf78d5", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.1.tgz", "fileCount": 8, "integrity": "sha512-Tw5kUUOKmXGQDmQ9TSgTraFFS7HMC1HG/B7y0AN2G2UzjdAXz9BzK2rmNpCSDl7g7y0Gf/VLBm//blonvhtOTQ==", "signatures": [{"sig": "MEUCIEySE/H6PigORysREeRRFSQonkAH90jAaCiMuJS4rtw1AiEA6kEc4Jpdoe8XDTsB+6aa5/d5iJhmE03bC7XGhsfAF8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMvyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtlA/+Pin56j0crRtgbFKlM5JPnXz5AW1PjgUcknHdkSzttSR2hH+F\r\n8crGXw4ohIJ56pTg8ur4QaCyoKqcoFy89eLJDGJ4L1dAbthahlGVJ4sN4aPd\r\nBQBwwdtq4yo+I1APcXvZq6r7BpXdXZ4M/m2kdHeh1SukG/pg4rq0fWDXdoeT\r\nKHrSmjSiBNcmQgGeEhw4RuT26kcR5J93oNgyGwViNcw5DE4k/RMXfh9RiqXB\r\nqhZYNRNvVbI+CiBuEVS9ukyMWfRD1hhy1LWMoGANmVZhku5mXd2/IAkSex7T\r\ncXbNrFkSNzKOu8HmyypHtTFPqmcM3EhcbdyNpYJaVoahULibLbWWlu3vo+Z2\r\nKoTZvdXztBuQuEt7SL0UwKZUw2RHvsvj05gTRNlM5hMZ4ZVJXcctxHbQMEV/\r\nPAnPTf9W6xDQ/jRdrmndtVpW2HR5Rp4eg7qy/PAXQOfT8/jd0YcV5bpO7Scl\r\ngEaWYnHsbxrn+J8D/EnT8JeGb+DQqdySZIABHXT0rfRC8die/epGuQefsyap\r\naRVxWD2y9hzxdlxO1THCUsqwtirScVTptiLBjySjSqQHb/7mc65/2AS9Vf8h\r\nFkMkfV/tmwvtB3HW2P7Fd/Vj2Tpl+VjEWJDKw9cbk34muY1sjpnBhYlIrdDC\r\njcAS0PKxIR0sStkqO9lYwn4sY2Lq7pqUlZk=\r\n=f1/B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.1_1661520881902_0.10484712464549761", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/expect-utils", "version": "29.0.2", "license": "MIT", "_id": "@jest/expect-utils@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "00dfcb9e6fe99160c326ba39f7734b984543dea8", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.2.tgz", "fileCount": 8, "integrity": "sha512-+wcQF9khXKvAEi8VwROnCWWmHfsJYCZAs5dmuMlJBKk57S6ZN2/FQMIlo01F29fJyT8kV/xblE7g3vkIdTLOjw==", "signatures": [{"sig": "MEQCIAzzVkBn0FadjRyWg9EasUh97TpVUt1x4yYaFFhKTI9BAiAUo3eW5E7cfPnY4ZzXOQoxpFUvxQ3EWm4//GkNXrRhCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzDzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrU+xAAkPLshx7OFJMYdu+9waupRu+z/RffhzYLbeW1Wh+boUOhXHc3\r\n67He48rvmIN6NFzpKsFSavL+ar3/WizQbu6QhakK83HwDGK8RjbFSX4rlAr9\r\nK8Tul8bBixtGWSeHFVjp8BodXcZU4+ebpAfIUCuoa1aOtGRXgX5K1p/kzs1E\r\nWdbo00+5DCqV6ZmQhqME6IasbL+bpPIFCw7R9yPHWHRREv2ifn92uKClGbZr\r\n7MbzulACL5X3BBukLXuCOYUvJd3akPJd6f+63/8LeHZYJUE+FITLQVfYqAG5\r\nTPHMxVAwA2cwYbsgIhz+lyJ5SPei3nWmO+5dc3N/PVx9xQu83btwt85fRMn8\r\nCsaCa4kzmA3dEIt5G18HTKR8DKyjCZvrvl4OnLUPVss5NYl+ArBA+Ix3Tn4J\r\nTCh7m2a70UUuDPSxgbor2peByrSrvzOJD+Q9LIJhVH2iMbMH6VsIA/kM2MkU\r\nVPje7hWyNHUAbyqhy2I1qOUoqcesuhnw0OjcUmpt2FH83u/kfCNiOxUMoTO3\r\nZtN0Cq4YYrZxRCJ4um07/02r5azSvz7LBrbzA5BpqMY42XpYsz+j7DhBk9gH\r\nJEMsqrrhO2pEDOZ2I8+6F1sd7ipCNTc9kTMEGbxLEhl/y0gh8RTTWjGBfzXM\r\neEcTu1Dwafl5Iky3nY1xxdC3R2T26N5jnB0=\r\n=OMZ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.2_1662202099482_0.31660297374232527", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/expect-utils", "version": "29.0.3", "license": "MIT", "_id": "@jest/expect-utils@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f5bb86f5565bf2dacfca31ccbd887684936045b2", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.0.3.tgz", "fileCount": 8, "integrity": "sha512-i1xUkau7K/63MpdwiRqaxgZOjxYs4f0WMTGJnYwUKubsNRZSeQbLorS7+I4uXVF9KQ5r61BUPAUMZ7Lf66l64Q==", "signatures": [{"sig": "MEUCIF0PpuC/GDWDMUA/zQZuFAJOfqZj6t8xPs/IBsmnYzssAiEA5kktjg+mXc3e3yf9J3VqTDK+A3layoPA+S8ARofZheY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkbBAAl2zIqXlACspyybII9TsihuA4he5Q1s5/jGVfa2LWb9LoXDDB\r\nGQoxgrZ3hNfFYTAK4grAt9L6bwPH4iS0Tnn5NE7f0JxAxrRTvy0NkiXccWdd\r\nRgOB+2hYXX1fQzWOLvRdsXccwsf9ynrfzBekRM8o4hMHlcO1Hx3ALDGgpU8t\r\nbhM5Gnp8fkS8J23Xp2+oinebBvx2dIKKcw3nybK2sbsDR9eqRgFBidbfxkb8\r\n4UaWeWzJl5ZXkMnN+xbbv2vY34U60tsdCAR38tjgznOye5gGqFS9QNsWs2tK\r\nVeqAQJgplAS2pMQvlcl9IgtRAXPIbQRv8kRHv4hR9MwjJ+HcvcsHrix+d/vr\r\nVZlXpx6vd0YgNxkTOEzLKh/u5lPXEatIQRYPKxIKodCAqrr+YTJYCTyyuetE\r\nKgzamMFScsyLGi9qBteD3brqHqB65yjzAjHINj0ID21dizvmPDBMf1ej3Djr\r\nbRs6GRFKw2kAYdIZDwKKTfIthS1TBM31GNliVgeWGig0F3XwrDIIpuMnwaDj\r\n4fQYSN6cqwx+0WTFZSG7I3PDPpSxAE0k3vtDn0CzJ3298Tj+8bXyfgCAgHCu\r\n/6mzOMAdmaJDRoRZxx2KG965Rpu7EqUwpH0+NivBwkuE/+STrIDUojTsnRLP\r\nbNgYWdLu/maLlS3SSAYZR8ajEevPgDEtR08=\r\n=K63U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.0.3_1662820898128_0.16590065771073714", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/expect-utils", "version": "29.1.0", "license": "MIT", "_id": "@jest/expect-utils@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "598cc7fea83b1bf84249b3b98683575cee888894", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.1.0.tgz", "fileCount": 8, "integrity": "sha512-YcD5CF2beqfoB07WqejPzWq1/l+zT3SgGwcqqIaPPG1DHFn/ea8MWWXeqV3KKMhTaOM1rZjlYplj1GQxR0XxKA==", "signatures": [{"sig": "MEQCH3PRGSRiBI0c7CmayjxsHhEJFJvMQ7tz+1YWI60D+dwCIQCb7mQCRfvYc/6RGAyA54N9emgOML8Jx6jI2VgppAMKFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/FQ//XGEcmWFkzfHMlEZIfwtFR2RZfusgBpr82dyNdVY+sHgxYjQs\r\nnFv2ojuJLx1XiR7F5Gtn4qLPPGRFTkksLrqgK8fiAyHgsrVKBaieBcUeanHt\r\nUaTxh/MfHqs/7Q1ENOOMjW3RFDNY8fyUV/bpPoo/r/ECqHm8Iv1Xic9cbucp\r\nViWZncov0azpN3DZFSbydplKN9AklzZeZDQx/I3Ic8tBpFcanAyyROTsJrFW\r\nq5aqy3DwQzOcyLD79JTFasF2nP23x/jbEIU+iYuxC73xf7zI8eUU46+HgU2Z\r\nNkead1GI/xGMgXsg1lxjutdSTVGL+f1GawujHnlRqa2CIYYALOoMgCymTz1u\r\nXCX1wtZkv/EDECkijqRTldHyaKb1yY/8pyvh4RhJQ/MQG+//4QToe0bCesr/\r\nDGITmKVmvDt0hoSd9fEitjQCyjMmm9N+HqDxLVZbQf43t9cyJV1kmRhRJkSj\r\nOT/o3WXkJGJ3jNltPXhcoJdUxLF17cVt1Bixfc57EO4LrFPDQ3oQo4ti+nlh\r\n150TSf+VzyvkIUsBzCBULlI2QcM7HFkR4qAg0NwJ7utByFga438mpyFph/R+\r\ncOkLgdRHx2wWFfS6X60olwSpYA2zmkGXWZ2RmAAFMgp1MdhOLkWAG7ut74Lf\r\nxgyHQdGZuLHtjKBF+hGa7gSQeEGSS4GkhYs=\r\n=v9Kc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.1.0_1664350664844_0.7164896753651204", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/expect-utils", "version": "29.1.2", "license": "MIT", "_id": "@jest/expect-utils@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66dbb514d38f7d21456bc774419c9ae5cca3f88d", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.1.2.tgz", "fileCount": 8, "integrity": "sha512-4a48bhKfGj/KAH39u0ppzNTABXQ8QPccWAFUFobWBaEMSMp+sB31Z2fK/l47c4a/Mu1po2ffmfAIPxXbVTXdtg==", "signatures": [{"sig": "MEQCIHkzRth2nHA3B0sWPJpHicnSg2y10xjqKX3Gh/yghVcrAiAzTBm/gE6wR2QlvQqCJiy7SPop4opMx3A2bxONKSVcEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7VA//eXTsKZPGV5nvJz9qm06hVH7SwHOmT0f59jMJsekAAwnsa89+\r\nGm1ziMKEctXNrre/OzcY1qOp54iqCsatlrMZA5TB5H6dir1eir1EbOP6ELoy\r\nxYnBO10KGH/cccwqQfLiTWwP+f60hhWtLVJDCkNHXjRD1GQU2CGv4iMgDbHt\r\ncy7+Gxgf9CGI6r4ijn+0MXD38UKfcBlDQyo+S739N4ldvQwC9pkv8bLKGNsk\r\nxSXRwItKyrB47BWCnbG1AaiSmPJkdYOtLoF/VnAt/mxwxW7MDPOq/DoQTxQ9\r\nI4C2/zecIuDpAIUpszcVvg5yRy+yAMcgmFYBpEk8TXiGxA3nzrsgmG/nkB8C\r\nRl3wSUFzcbNgdN2nD2dGOVfzHGHjsa2WfyRqmqFXxYYEIw41Nsha8Ipm0wjf\r\nFK60fmiYg7RxNOXNmARjvzPgqqLB85LDFUn14ZiQ6m5cfM/iZ6NVv40Wfon2\r\ndgoAaeR/ezB6TeBiYuY+KBM0hJN49VVunD/SiVn5lg6A87n7r1S5cATUe9Y5\r\nACMAf/hhMRhxjTWmSYOUT3iqQpLc6bLxxo6rUvjPWNFsCvpjF3x/sjJZ7B9+\r\nlHgQAnySnk4xRwqCod9rr85cEX9Dw2axpIX9KcVHZLZTa2YcBJqD2nPvtmm4\r\n43RHSQ4xJY1mANdn/+eRnbtAQlNqBs/vQYs=\r\n=1Qa9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "^29.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.1.2_1664522571753_0.6797823360214617", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/expect-utils", "version": "29.2.0", "license": "MIT", "_id": "@jest/expect-utils@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3c0c472115d98211e7e0a0a8fa00719bf081987f", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.2.0.tgz", "fileCount": 9, "integrity": "sha512-nz2IDF7nb1qmj9hx8Ja3MFab2q9Ml8QbOaaeJNyX5JQJHU8QUvEDiMctmhGEkk3Kzr8w8vAqz4hPk/ogJSrUhg==", "signatures": [{"sig": "MEQCIDUPgBAImooKoYRyx3ppYZOlw5uFNUii4EWxaQ/Z4htoAiAWE+7pDQAApy2Q8XvEcFiJlY7BOn0QmRn/PWpBy2jAEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7+A/9EJIoNQb6HPM2nQoAcj7KtyVd1dNkn5uDAYniLIF5oGtPdPjo\r\nfxSNRKvmJLaBeGTlNjcHj/vYlRDLkk+QzH5MnHoPIF8HyZJZA0K7kYhwjmao\r\nZBDe15CTS/yrNlY5nTBKeA1sVmaBCIlGWq/TLI8zOiS3mghYDI9G/WbW9Ds4\r\nMBWLMLwRbyf+y9fLZmSq6PhNgfbw3osSLqB19RCkhH5i2zhXMnTtxalyCZ9V\r\n2Lz5p3eQrDdQKNx0rm+P3rrOf5AjyI+J4pe+230kvFrAuV9Xl485nUvAC262\r\nMWTmFG541HxBQW4GbqOdoi5ROJa2TKn9IYBvsKzxJ4leWhME+zfEPC4bxXWe\r\nYdCGsZnQvWkHBdHHbJwH7YmrpkF+dHYC+aw2X3Z42fDGqN5GVdMfLVZJf9qF\r\nxUC4e5rIpEkzwtu5GFCKiDXVOHFB3otSwW6dxFUfbJD6Ss+DPqTWLspt71Bc\r\n4uRcMyCkHkiOqYrbxgPqxt/9taiNz3XNdnNCEZmi9gpoVl1fTah65bx0sO/T\r\nucqSWU1RWtU7/EvdrcN7PraehrAVs3wXK2mn9INYqqYSItLSxQef8y598n5x\r\nQzWkRFKZo3/BpzPtAJhW5YidXEsYSSgtEOdOYn1YGFoKSoMjVYMHJPwCdbR7\r\nkEDij8DDWvEkth3kIfQJRLY2ROvjIUk/Nho=\r\n=8W0p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "~4.8.2", "jest-matcher-utils": "^29.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.2.0_1665738834183_0.9153980500046872", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/expect-utils", "version": "29.2.1", "license": "MIT", "_id": "@jest/expect-utils@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eae61c90f2066540f60d23b8f254f03b7869b22f", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.2.1.tgz", "fileCount": 9, "integrity": "sha512-yr4aHNg5Z1CjKby5ozm7sKjgBlCOorlAoFcvrOQ/4rbZRfgZQdnmh7cth192PYIgiPZo2bBXvqdOApnAMWFJZg==", "signatures": [{"sig": "MEUCIGvnAJp1IL8oTsBEJkwj8PT1DRwb/TuBpZsBoXuQj2lvAiEAxiP6jf9vOQPq0xDp7mIZsjl8iSvbHR94xD1Y950RH0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC4w//V9RP74c3E1XpNIeAQCk2pKjT5WbPckyPNIbaAuzhEP/BM5F5\r\n8IGFB1orz5/8oPwofIV/7wttpTfvDBlHHUsEOa0przhyHrNPmcRskl3vqs8t\r\n/a15MuclyHa4/688OGTEyVs+8c1RyzEAt9mYHsq0cPEFbPmd9Pldr9jTpzoR\r\nYhIzBDLdv6rO6d0N8KYUXtx9YoV2PosBE8SssEfvRHp/EZToIeW5S7bNBM+f\r\nqDhGL/+IUgZkteVFjy9URDRAL4VYRCGqKIYN3tFDlJXD83eHVQs8dS3kgFwy\r\nKgIEyMOoqMGRC6FbrVYOSO1rfNaJ5LpZfMZ/nHpJXejMRfaK8Fq7hpZj+zMy\r\n/hrOEFRsxzlKhdfWx/fmVdauCwun/hnbvtjxf35F3kibK+CvzT08SmwBHGzg\r\n7DgC3uka56ghu7C/lShjuCqxQKvAtXhpbqUOG9QXD9jE+UDCtGyq1W6SGZ1k\r\ny5rCE4RXWcnmJWELKd5pCYYY93FvdMml/EzPfnJfAwJr4hg4v7qnrrAAdseL\r\nciPP5M/RW+lnsUNFTNftp+TuYcJQ3jLX0Lclup+PYaAWWYTO+ci420OLBjde\r\no3rYmJjjsmOLA4V0xNGj3xCkyH3DwV4mOWztdNLtVwDVrLNNiXxHpZCsDDsH\r\noAyvrZ6vUljg2xWChIdFIfgbrAqvTG2uqC0=\r\n=4kVh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "~4.8.2", "jest-matcher-utils": "^29.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.2.1_1666108817067_0.5279790235228012", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/expect-utils", "version": "29.2.2", "license": "MIT", "_id": "@jest/expect-utils@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "460a5b5a3caf84d4feb2668677393dd66ff98665", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.2.2.tgz", "fileCount": 9, "integrity": "sha512-vwnVmrVhTmGgQzyvcpze08br91OL61t9O0lJMDyb6Y/D8EKQ9V7rGUb/p7PDt0GPzK0zFYqXWFo4EO2legXmkg==", "signatures": [{"sig": "MEQCIEzMoKqHDIc/o25U2bxe74kgkp2dOdqSslK7ayQo/4ilAiBgK++4htPDcbdxJxouGR4za2g75xlSaQfxg1IPXcVYUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe9g/+OWwZbPwI9jwksfVPTDvOgdDuF3tuPG72xIwOc9uSur8b1tct\r\n0OdYtN8CmAVUZJAHi3+B37qs28SzuZ8XbmcdZq+mCEZLcXex6l1xA4PhckX1\r\nUgjZUvDNfgwjXkvp3htJyee90/8w1Eh3/xOfjdT0iQSylyTm9Kl94f7b8NN+\r\n412T7S7ix/u+oMPbaePWUD8uly+bEts5b/U0CejPZsD/y8twquPk8hKdT0Cy\r\n7pgeT+eUuNXR1vAmWdjEBufKn/zdPmRdIOdNEirlQK25sPga5kR6Blr+nGzk\r\nHqBw2eeCMojjTyRD1lY1sqLvvJNqwACjZEdSBp0X2NR4CAccpfamB716+nkj\r\nBtVwXbdZi6m1bX7U7METFOh4xd571G2MUiqmQ/I54F4XXd3BU3gTASq766A6\r\nZIAhJCMMZNyWCLOpMzkyhNMn9HY4kPFn2V561qOaj3G8RWMPy9Q6BJcRPhOp\r\nOWApS0U0Bz5y8NTFd8up1XBhP/H+SS/C2HovNoLcoWEP/9tU+dBqJcCpSsmk\r\nBOddWZ3YhL5+3m3McJJI93yVyx2dFjrePuNEGnlKPSgj0dLskjEs5vO6xKXW\r\nSZeRsSW2CWm7NJQ+siSzydiFbpEW68XIGlsf5dQ+8fUaZfDQr4ojlNYbPvt6\r\nc6mPpIN8Mi6142nlEzu4B9WGvSgPrVAnghs=\r\n=kZcJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "~4.8.2", "jest-matcher-utils": "^29.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.2.2_1666643047246_0.4190576078118746", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/expect-utils", "version": "29.3.1", "license": "MIT", "_id": "@jest/expect-utils@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "531f737039e9b9e27c42449798acb5bba01935b6", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.3.1.tgz", "fileCount": 9, "integrity": "sha512-wlrznINZI5sMjwvUoLVk617ll/UYfGIZNxmbU+Pa7wmkL4vYzhV9R2pwVqUh4NWWuLQWkI8+8mOkxs//prKQ3g==", "signatures": [{"sig": "MEUCIE/4rbOziy7JW8a4OF+pc4HQWpwqno7sstRD4DC590WbAiEAsGVNw1TV1byiIPHLwxzyvTfl9U0YgkxnT+dA6F4ExKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP+w//SymIECyJWqg9uaP1Iaq46ll1E8UDeJE5jNQUoCh3Q3s9K1g+\r\nGRGveq4GKklg+xsk65eqYmO5mPJBZjXv7p4dxEhGQnsSo1gGIXp/dSuvljtn\r\nJuYkNq6Y4gGSSzw63UruSb4qgNAFLeQinxvQ1kvC6MPnYId7zFyvObN2yS8f\r\ndXsYJfnpOi7KT5i5Uu6lDLtCmCT2q0pJwYgwDTefaxze8729UQ2A/xQzWJxu\r\nJFYWFaOdJum8K5c5nnmLMJi2Qx4FoSwIUEjEuplSubuqUteZl8pNSfvhumQk\r\n0sNAv6MbA+zBGa3u3SQWU1Sp4V2kR7NfRsrgAScrnm8ui8Qy2mlwPyh7lrWy\r\nWKrruBgRqrR8HTIyaArhY/cZANvHwIqeA41tKkped9PMeyPk8/HknDbF5NcY\r\nSroqWEphU9WRVNALWRWrTZ6jWrA+t4XZb4nD/O2LbrsXC7h8YTmwZvZIRFsa\r\nqQiVADZSeg2P7/fkrcrxSBOapC4oBGRYJB7IpOecDk5+EG+K6OvD5K9bh2Et\r\nseLC9CrVQEP7SQrSS3LGdTtp6D7BI7YzS6cs7a6gBwa4MrNTadlIA6hXiMc5\r\n78bVVKPIB98Imw7ZoYNoAIojvoOCcfQ2db7DpRmzA41iYtqYXL2w4yZUTObl\r\nkSQMDo9OC9tai7eYPATa9W4+onU1y2IF44U=\r\n=Iq7G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "~4.8.2", "jest-matcher-utils": "^29.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.3.1_1667948186135_0.9912048387669392", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/expect-utils", "version": "29.4.0", "license": "MIT", "_id": "@jest/expect-utils@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "97819d0da7027792888d9d2f1a41443be0baef80", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.4.0.tgz", "fileCount": 10, "integrity": "sha512-w/JzTYIqjmPFIM5OOQHF9CawFx2daw1256Nzj4ZqWX96qRKbCq9WYRVqdySBKHHzuvsXLyTDIF6y61FUyrhmwg==", "signatures": [{"sig": "MEUCIAkAOcx9SSkc+BBvFO/T7UdEswOH8GOh0sw8jrI79sKcAiEAkVoAaT9AOnVG7Si8V4aaZ01wpnliqw19xXMDFu1o5QE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrh2BAAioA5dgElgulP4oMh+N9PYbSmibx+rTu6aGP1C3j9oF2h7l5+\r\nP0tN8hdGDXGJts4dDv7wcvmXTzWTs7Gj0C5WL7X53BVUT8ZYwxoAhIVzB3JH\r\nHy/IkyO5pEe4PJqLo8QHC15DrAVYFizfSNrku19Zr+A8RtO/Z4K5Uo3tDsTQ\r\nj/Vad4QdNuRa5edXtVCIFbXeDktnKwPWio1pmCy2LsTEGC3duo4j5wNcKkx0\r\n0lTc7Xy7Qkkm/BwVFICtoA/wDXukgfNEYDJQ8EXIHPHchjhn2PxejEsz3ucO\r\ngdmd6RFAIlwfbyQij8wccjLC04ltGU7eqMMJ+DMzy3wbHwf3Yfl0RDD4+2zA\r\nSRS5YcTAR0crOrmfYGmZmbEkUnPk0jDL5YKxxBCY0Wsm2VXqaxaQVwm62j/2\r\nUD6M6rQAmUWhRKdwwkzF5a1zneIl+976MSZyIEkCvPNeFuLwDnBCqdvL60Js\r\n1EpOnPDKF4kf2JAbn+1lBhY4WsDFkNFdYo0hiHHSsLPOO13RPjiMKLuAWYZc\r\n+uRDIDTc7pA/W1AGPvOXbUvbdj4hcYpNiUd1RQnX0wYoMgwY+n98nO6nW0M4\r\ny34GPYcTtTZy0+cn/LtJnf6x3R9edN6AB9ShR4qIwNB0AY2SWTWM29BfvfBx\r\nG8GL1iIGdw2Eu0W0UqbJ06iQAGgTMYfDOgE=\r\n=yuZU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-get-type": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "^4.9.0", "jest-matcher-utils": "^29.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.4.0_1674557756401_0.30832629448065574", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/expect-utils", "version": "29.4.1", "license": "MIT", "_id": "@jest/expect-utils@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "105b9f3e2c48101f09cae2f0a4d79a1b3a419cbb", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.4.1.tgz", "fileCount": 10, "integrity": "sha512-w6YJMn5DlzmxjO00i9wu2YSozUYRBhIoJ6nQwpMYcBMtiqMGJm1QBzOf6DDgRao8dbtpDoaqLg6iiQTvv0UHhQ==", "signatures": [{"sig": "MEYCIQDgtg0tnF4F6oGtHY4r+hYpzwn1uMDuC6b/E1awuuBbuQIhAOzE3JpXEe0fhJk+ejczW3ImJjzFrcBhjO/Z+kuvL+wZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4Jg/+KJAAL/xubgNgQj4phEVZnyUD0eSbIBsASluVwWGPfc3ivJUp\r\nLapDeTuNS+8Cz2lMKyOZI/bGQTtSQZ2IH7LpzHqnXO6Y5hVAV7z/HchIR41N\r\n0Rfohkp9goRbCsD7Pbe8LJSzoOkdf6eP7TfBEUXahva3fisjG3l2MRIVpHgb\r\nIRvjsRL+1JJfmOxRpV0y6pKv8mpgG5Nj/GccWFKsJqtLkhaEe3rZEWoZzWBK\r\n1OCfTBvUAqR6c7wsM1TLRU7iecKh6y4ybTg7is/zzg6MLoLBb0P6j+Slh7Lc\r\nS7NLv0kZJCvzBV8HZWQCu3f2owwdPnMHpN3O6LTu2smPYM2pjsKH3i2rXl36\r\nWJX88CneJX0aF9IOcB6PFZ6N2Kystb9yXTlyKW+7aFp8ypSrDREOIi3qfBeP\r\n5Mgk+IIjhy2aWCwNVl3iRJscdhhRZzjoJURtxcpPQlcb3yuba5mG+UU+60J4\r\n6Y1P5Zd1Sxxv2xynl3b3SaU3n70EOu+9y45KDkiyi7vtL7kt1UuUHomkSVfj\r\nJXqfwaLzYCIPdnN6nF7hU9rah4SIcfuslh8EUrL2ztuHEto6uucMlivSIK/5\r\nSdA9k7r1OI+jxZKwtpggfqOc2+PgzkwIRqvrlBug4pKWZ7vTBRsPUIJYNEb+\r\nY47YQFSIJinnArDm9UkT9HMABoKOqu07LXE=\r\n=7p2r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-get-type": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "^4.9.0", "jest-matcher-utils": "^29.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.4.1_1674745720464_0.4220636708924044", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/expect-utils", "version": "29.4.2", "license": "MIT", "_id": "@jest/expect-utils@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cd0065dfdd8e8a182aa350cc121db97b5eed7b3f", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.4.2.tgz", "fileCount": 9, "integrity": "sha512-Dd3ilDJpBnqa0GiPN7QrudVs0cczMMHtehSo2CSTjm3zdHx0RcpmhFNVEltuEFeqfLIyWKFI224FsMSQ/nsJQA==", "signatures": [{"sig": "MEQCIC1ezFtZq+BzdUmAjGEMgD+ljFClkZ2gEUxizqh17IoJAiBohsKHw5B01AYm2NUhmbSZbFZXzCZqkaON5VoBSCViQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpn+hAApLcC1GtvDlge5wJ2PNJwV8CYK7mHSjh5jkQUBeoAz1J6ZS02\r\n98dzAzTHhgRXydglnFushwSWN2yyyvULUVsTosU5PnCe/RhNKudAnrpenF3e\r\nrEEfV5t+6cmqek+PINsvs/+yLdHOOtc6mZ5apTmvpHnZ7JXD1RgMFYBBYq22\r\nSVRmRUFVLb7hU5tkp486ATf9hZ0Ld/mFa4m2IRzqenEKoCWA/vUi2yrKuMg7\r\nJsH5+xrEVnzFhbvYG4AZ7zAqMH6OnXCkEdm+qTRsBgXEeS64PWpefqpVe7+U\r\nwjk7fP3eZoo2m1pb8XxnvXTxqXGZwvRm0gy3Y7dXl8ugPVBuDnizAKp8DFu2\r\nQeZ4DvryPoYW8nA2V1V2+Wuvf0rszX8tBQePuc7noGUR78pNkyW8qzN/n4/e\r\nVS7ld2apvqS4VOKmf+f9ZZQi04xAtEa7gN70QO+rbip5Dn6JsBZxDvwVaWkt\r\nW6p4cd8i0EKrVpvySirF9m0YpwOoSJV59DfVDyAmBCiekPHEFeABiJu13iu1\r\n+6aDYBmU8jTqh/ywmBtUT0PerkAt1Vt78x6/DdKclHXpoRFpqmQeYksFfo2H\r\n05+MTCEsqgC38iP4BS4QR9U4GAtDHCC+uYVL6AAzjbW/t9cQY4/k/ZRBMzgS\r\n4BGJ4Rza5/n/mYe8w6YxT5p7nW44TWJEApQ=\r\n=rvp8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-get-type": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "^4.9.0", "jest-matcher-utils": "^29.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.4.2_1675777537378_0.4307318069271018", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/expect-utils", "version": "29.4.3", "license": "MIT", "_id": "@jest/expect-utils@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "95ce4df62952f071bcd618225ac7c47eaa81431e", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.4.3.tgz", "fileCount": 9, "integrity": "sha512-/6JWbkxHOP8EoS8jeeTd9dTfc9Uawi+43oLKHfp6zzux3U2hqOOVnV3ai4RpDYHOccL6g+5nrxpoc8DmJxtXVQ==", "signatures": [{"sig": "MEYCIQDHPO8jQOfH1eZRsoO6iq6iAqoi2SnUd8Zeexk5g68uggIhAL1jglzDlEepVf5+Smt7UH70LLttd4g0dLW6+WiN0zX0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MimACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtnhAAmCud1GU9aLfvcUKUygs0+2xye9ZQPfLbInr2fnNwWTVuj26N\r\nbgSrhvAvDTxMMVkUKaXYg6aSmuwZ11Vlg1MTMBxNZDA0MLT4mU3IN2V8VGBi\r\nWZQ6Kstt5u4Hzipt/YbarTpPXMCez3v5NSuhs7I96PIEJnNKD7gKL8Oo49Fd\r\nMp5kgsqrijAR35Gx6vphWD9BVhd+zM+PZUVdACDcTLNdTegWJgsnTDD7gAXO\r\n36lR29Yj82CWnwT7fKHuIl6dOnfYuFJaVNhybysDMFk6NwV5NYecbEhTGE4n\r\nbxIMTN87/7oHKyKXsdOAFhFXikHe9xn0Ioj1VEVO94hmkqc7zVvallGkwYrB\r\nQE89cmtiUGN9OSw+y6NTDJm1NCu3z8kJneB0V9Q+829DEe1+CtUzxJFZcCxy\r\ntdVCKFZjYtu1l7mvknlgFRMED8OMAsuy2VLH0M4mBl0W5D2OgaeV3mvRWDJz\r\n4ZbWyKr4B6NSdWZOZEzLzj3IhvfDu1SeR6WMq5dOscZBFI3n+Mhuzn90Rf0K\r\ncdidT8uT72FIMTL3rcb27mYOHKYfrrGfjW+yPuMF7CMtWC6016xBchRxiPy1\r\nImoYCgcPNB0FLLbKVrVJsRfvx9GjRZnmhMrIdDS3IhjlS5fMcZfmZFQ9yMgQ\r\n831uO1fhT+yUl5Dq71jo+42XM0S0vynaPdc=\r\n=zhKz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"jest-get-type": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "^4.9.0", "jest-matcher-utils": "^29.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.4.3_1676462246159_0.4658344028718364", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/expect-utils", "version": "29.5.0", "license": "MIT", "_id": "@jest/expect-utils@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f74fad6b6e20f924582dc8ecbf2cb800fe43a036", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.5.0.tgz", "fileCount": 9, "integrity": "sha512-fmKzsidoXQT2KwnrwE0SQq3uj8Z763vzR8LnLBwC2qYWEFpjX8daRsk6rHUM1QvNlEW/UJXNXm59ztmJJWs2Mg==", "signatures": [{"sig": "MEUCIAV/7ZPg6+NuQu9M04QMJkXMXrz/Rw5h1TvFWbYUDAuOAiEA2ryAy8MYFOkywEGJchx1xjVOvqkTTFkgkokkgpDD6Ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeuxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpctw/9Hw2dcyIhUju64c8ItX8aq1XgxgxNxqWDoQ9oCijeC6rJpvd1\r\ncq1lHSh8YXueNLdrYssRwlZ6tSzxWqVVEksOwQvuaTDb38hm0u9WrR9beKh4\r\nrtEi02QwwcqG446iGN7gDymQx/G3SNMIZgqjt45wk/XXCwR0eO5TvMOowgfo\r\n2bv3WUB+SGNw+ji8Om1LmMAZT3Zc/h2QLE48tBm2GpeK1o++qjac7xzsS/YL\r\nYhFlOfc6ZMui89bggDS3geVe30me8SIrlFj745SeNdMgsipiK/ShNpb/8X2S\r\nqaQMW5WirrqOHooj/nN9K1gCLq6jJRe9fMAmTRo1Qx9E4OqIRcyeDhumAQF/\r\n7eTJH++L9OTyQkNMgqOpZXfT7zfjgnmzWCQfogfYlVZyj+8M3UOcGup/k7Hg\r\nYVW2+G3t4XhszKk9Whi82Ap7RMzM9bAmceas1yIWQxsuWhguCMTmDVjMduiC\r\nhaogQdQZvja8KZGAy3ovyt/5wlgJ47AjzS6xbQ0W3LljWCjWMZGPwkmBjRTi\r\nJ5e5TkedFLDvZPvJrBTj6fe5/xfUU6OJhhG543GgXSqYEQIKXcGETrHp4JmW\r\nAq2y247vxHTdo0yMJLI7NtMBchwe4OU6IaNkmQVJVGHLbkurlACPNNEZElVS\r\nh8wn1MCSddLWaM3kj4TobnbvhyDmSOQnrwU=\r\n=1QC0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"jest-get-type": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "immutable": "^4.0.0", "@tsd/typescript": "^4.9.0", "jest-matcher-utils": "^29.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.5.0_1678109616985_0.06537659328710843", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/expect-utils", "version": "29.6.0", "license": "MIT", "_id": "@jest/expect-utils@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "14596ba728d61b0cf70f7d5c8fb88b8a82ea9def", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.6.0.tgz", "fileCount": 9, "integrity": "sha512-LLSQQN7oypMSETKoPWpsWYVKJd9LQWmSDDAc4hUQ4JocVC7LAMy9R3ZMhlnLwbcFvQORZnZR7HM893Px6cJhvA==", "signatures": [{"sig": "MEYCIQDBvP+hfw5xbQ7pgUmLQTfmHCxMmDknruRWbHh5ahS4lQIhAOCug8XaiGZDmcY/n2/Fp6kBdJu67gkVBjtDrHrXiCyi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28188}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-get-type": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "^29.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.6.0_1688484349840_0.8984270497353337", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/expect-utils", "version": "29.6.1", "license": "MIT", "_id": "@jest/expect-utils@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ab83b27a15cdd203fe5f68230ea22767d5c3acc5", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.6.1.tgz", "fileCount": 9, "integrity": "sha512-o319vIf5pEMx0LmzSxxkYYxo4wrRLKHq9dP1yJU7FoPTB0LfAKSz8SWD6D/6U3v/O52t9cF5t+MeJiRsfk7zMw==", "signatures": [{"sig": "MEUCIQCh0+3kXK7z5xB0FGQyOurAg4FJYeoFLMWSk79d8IHsbQIgB4rVsFLl70CeMgVeOSolJP8PbVwaQB3ACcAi9yj0NN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28188}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-get-type": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "^29.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.6.1_1688653109537_0.12693514668737338", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/expect-utils", "version": "29.6.2", "license": "MIT", "_id": "@jest/expect-utils@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b97f290d0185d264dd9fdec7567a14a38a90534", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.6.2.tgz", "fileCount": 9, "integrity": "sha512-6zIhM8go3RV2IG4aIZaZbxwpOzz3ZiM23oxAlkquOIole+G6TrbeXnykxWYlqF7kz2HlBjdKtca20x9atkEQYg==", "signatures": [{"sig": "MEQCIEODvyXAe6/Xc/J8nQLtBmcY7sIdf/sqsaw2eveH0LadAiAdCYs7DLkTeKuKuQas5ewkDheLkjapKoahJjwcrR2k1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28188}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-get-type": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "^29.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.6.2_1690449694465_0.9810299618451186", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/expect-utils", "version": "29.6.3", "license": "MIT", "_id": "@jest/expect-utils@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "5ef1a9689fdaa348da837c8be8d1219f56940ea3", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.6.3.tgz", "fileCount": 9, "integrity": "sha512-nvOEW4YoqRKD9HBJ9OJ6przvIvP9qilp5nAn1462P5ZlL/MM9SgPEZFyjTGPfs7QkocdUsJa6KjHhyRn4ueItA==", "signatures": [{"sig": "MEYCIQDdosiBF681IczhvqpMDvq3+zzRqjA/kz0PoptOah3/ggIhAKVgAiTzSC8hQ9iEs3lu5mRR9WAUD4lRS2m0uRl9EnFS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28330}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-get-type": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "^29.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.6.3_1692621561723_0.8425185153350372", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/expect-utils", "version": "29.6.4", "license": "MIT", "_id": "@jest/expect-utils@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "17c7dfe6cec106441f218b0aff4b295f98346679", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.6.4.tgz", "fileCount": 9, "integrity": "sha512-FEhkJhqtvBwgSpiTrocquJCdXPsyvNKcl/n7A3u7X4pVoF4bswm11c9d4AV+kfq2Gpv/mM8x7E7DsRvH+djkrg==", "signatures": [{"sig": "MEUCIQCupm4WWME92FtZtRFHdSZ9ai7aDR4cYnL5B3QPgm9TlAIgRcGz1KhXxiEYRYnjuLCfyuaV8KlBasiEVzFeupqHdP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28330}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"jest-get-type": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "^29.6.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.6.4_1692875444457_0.5122868856033134", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/expect-utils", "version": "29.7.0", "license": "MIT", "_id": "@jest/expect-utils@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "023efe5d26a8a70f21677d0a1afc0f0a44e3a1c6", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "fileCount": 9, "integrity": "sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==", "signatures": [{"sig": "MEUCIQCARcCt990/qLYtqixKcHKW1cQKI34KlAeGBS8xudO3qQIgcehDr+xCCLApe1by/0n4tKVCHoETaYB3HsfBPdWHLAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28330}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-get-type": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "^29.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_29.7.0_1694501027011_0.6548577822002835", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "98b04730c71f0bcebeae0dd63e4015c42066e9dc", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-Rsh1UgAjN2JcK8oiRtkG+G9YU4w2xQ3e8lJVd6prmtqbydzUbmRiqsDkCGFEpvtkgh3D/+85hA49q/9zIYQ7Dg==", "signatures": [{"sig": "MEUCIQDQkKuv96sCXn1c/0GTH+Jsky7p8vooYMziahgFHq6FigIgLV1hINL6lE3OpeHrLX9Fu4feqP5ORgVgW17BT/ntN1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30297}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-get-type": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "30.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.1_1698672795069_0.29404663170110434", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "367a82236ed81da1ed97720eb6b5124d3933c87c", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-0x3FURwGPcFtHlLpBoUv1izJElUAK6B8w8346Dkb6Hn/B+nqgDZzU7dkLek6MuTCWd+x+Zuye926xUrzJeTpUg==", "signatures": [{"sig": "MEYCIQDMYQmBJyLHFwEF4bhicNCY8xQHS2AzAWJyjm+w+31YtgIhANyY4bsciLP+6mOCGRK0cutBdplesHw4bJqbY6FOTrtD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30738}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-get-type": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "immutable": "^4.0.0", "@tsd/typescript": "^5.0.4", "jest-matcher-utils": "30.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.2_1700126913537_0.42192578931350977", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "feb38717008830b3cb977c2b3be8bb4c4275e411", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-1KoDS+Mc/7Jjb6r77wvHRCRK2LGxEidNw9IseyD79UDX8nwVw5t494sxnlUOQ2qGB7GNtZz0znwGX7BwjYFgLw==", "signatures": [{"sig": "MEUCIEb434MUEClwIigk2blf0iadn7xjutYTZMSeYU95MENXAiEAkwPs3eVUjAwlchu2GYza/XMFEe2wzemWiDOxBSvbEfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30704}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "30.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.3_1708427353766_0.9580129867894656", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "78229a3f321897a5fee9824cc9b518b818a763e3", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-vZmmZ2Jp+SJ1z6fdbUQaalpWMrOEqfCV06OXGuBAhhRIPttX6jsiZRru5nIiLk7OnfFRsOwhepVdHO3nPUYtMA==", "signatures": [{"sig": "MEQCIBgcnF7ZLpT+9YZvrjS9KxpqKarzz2z0UEzG9qsqEFz8AiAuI1yfDCOh32kbPRpKIQdUk2ssD4N+fRrg0Addc6FiwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30911}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "30.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.4_1715550211599_0.81019377842156", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d7e2afed5003bc17c606bc98d18b7ac279e14193", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-DwWb4vqgp+Wc/vyrPY3afa5J2PnrKpzW+ckrViWMhp5gu4hnktatBiad9/E8+buUEZWFZLamcqpqk6KtuPQBXw==", "signatures": [{"sig": "MEYCIQDGsmNU+bvPp9OJAbVEFYoZEdHcGHraCkVncSBiFVrwSAIhANFvcFn/QzI/vjkrbzmqOOSyCAGrRRok4QmfGjwiVt2Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30899}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "This module exports some utils for the `expect` function used in [Jest](https://jestjs.io/).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "30.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.5_1717073051305_0.055683326338630934", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "8d89187fe375b7804fc89fb4906d8650e9ec7c49", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-QMySMhaCUl0ZQd7Tx5X3fVWY5jtQxZNrTll0OyavdQ70ZTLgk0kU9K+XovcMWO26MK9R5EX7bBgD/j7w9hUM4w==", "signatures": [{"sig": "MEUCIQDF3J1DIBZQlLwTWBdJ8ICcU1U7+ln4dQAPhUaelAUclgIgE1XYt5EDw0RUOYDOh+zQsfVM7JXnXPdWyh/ej8zKT+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31241}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^4.0.0", "jest-matcher-utils": "30.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.6_1723102989017_0.9762453413716277", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/expect-utils", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "358d7e8d1a17503ba88a3db11ad6a6d5010f2f70", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-lr711aEHiXWM9X0BC/fH+HID5eatdCjluh2PKxKHPZbjmtWA5RiHXC/s16cPFI6voSnofOjDICvzNVxSO87ZrQ==", "signatures": [{"sig": "MEUCIQDdt6s8rbIct4RdRsGGdAw9hrA1wMniNQdo5N1bycFcjQIgSs5BguMENCCFXtWjn37Ox9dcA9kVTX3jBWZEeGkTyHY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31241}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"jest-get-type": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.0.0", "jest-matcher-utils": "30.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-alpha.7_1738225717423_0.7465087290116192", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/expect-utils", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "3881d92ee1e89bec57bc7faf1a98f252d469c160", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-YCmWYV3/dqN3hgG8WZ3liGzP24QJSeSIr/uZte2sf4XxyqplNnC0AjJqqCqFXLglIfVWYqNgTNK6gyXUlBRrwA==", "signatures": [{"sig": "MEQCIA/+yZyQVcwyZhdJMbe0HKP9HXLZbZXb/AbJlSX0/X7GAiA5PKwLtEzRZD4+SGhGTjYskD0tZ/uIshvxF2LxCoHbvQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31231}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/get-type": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.0.0", "jest-matcher-utils": "30.0.0-beta.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.2_1748309000964_0.6937395278279155", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/expect-utils", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "9c59a30715479e13319426642feaa0617735efb1", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-yiEn3srirtV5te5CqB9LE2YkeEjBwXwm+VZ+ckIni493hpdUrKJkYQgR5N06XO63xR552VM15ViSG1nqEis7CQ==", "signatures": [{"sig": "MEUCIQDumvvZjT5M2jlB9GTXWMOjyK2q0lMDqXA/tmbLQv7uLwIgClkxpcu4q93mvcTMNzEDOtaVymnBbRP3PGUDhASWQVg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31231}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/get-type": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.0.0", "jest-matcher-utils": "30.0.0-beta.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.3_1748309270000_0.02273395105355447", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/expect-utils", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "f0ed34b79b7d524c1d5cde0cdcd38f194e4b638a", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.4.tgz", "fileCount": 6, "integrity": "sha512-5JtbAAg4vxe9QGLhJuNPhGWI+cc1pCCJldxHPMnNNUUdueITYFG2y/BXjSZtYsslloue5F03Q2MQQGDyLeolQQ==", "signatures": [{"sig": "MEUCIQDOraOr6Fosj5CnuWib/ZfpUSztfrpCqCPrVUlcsVrP4QIgDh/sbpHuVQW1h7PhvZLplzob37QyzYGN8w/lDZ92d5A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31231}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/get-type": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.0.0", "jest-matcher-utils": "30.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.4_1748329468723_0.2460714026540729", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/expect-utils", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0b81233c8b493cc0e2327cec947217ec6df25944", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.5.tgz", "fileCount": 6, "integrity": "sha512-qwMtv4S8MqyGEQIH+RJ933xRcCp7pdY+ldMeWG33yH4/6h7yEPdY36KF2Dqdhtb43sKszfBeZicOkj2G6s3MYw==", "signatures": [{"sig": "MEQCIAZZLNucmBEpM0uPcppPFCWcjq3CPX1yaOTYwT4QHzcXAiBWWBBSohOTvGSEnxGcSAvW7fBUc5ZFdmNBf4rOlkXvVg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31231}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@jest/get-type": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.0.0", "jest-matcher-utils": "30.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.5_1748478615163_0.9446732202750703", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/expect-utils", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "12fe7504c70c7cbf09d67c13f8385bd4d9f539ae", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-nWTIh+Ca6Ufrx1RwcsxG+S/5R8sQu60Gbhb9fc/muenuu85GDRZLVD18pxzHrzJPM0pNH8Qs0Co1w7CjJtA/lw==", "signatures": [{"sig": "MEYCIQC7v7NQxVPbYgqgwfI6GQPpJCUzBZ9ryBEG9PysOoP+SwIhAMBWE0mRuHPmaEewjt11B9R4wXs0dkTi9mfh0BpwhH0q", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31242}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.0.0", "jest-matcher-utils": "30.0.0-beta.6"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.6_1748994652253_0.8991894296984333", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/expect-utils", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "5df20d17397ee44ab29ec0b775d5ab6d1796356d", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.7.tgz", "fileCount": 6, "integrity": "sha512-+1G7BI/dxphi4EbtEjQhM0K/DvXI7rTAY9jgeJJVokpABRLEdZXt/sDAYQkZlOqXHJba8Zko+pj52kWe2StY/A==", "signatures": [{"sig": "MEUCIQDBl8d7r1ltAzM4aRKl0chrb++pNDlC9iHuPFfF0qyWtwIgIOf9ZIt36Ur4H585qXl116TdwcqiclKMcIuTKojYpeI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31242}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.0-beta.7"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.7_1749008144867_0.6997288299057296", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/expect-utils", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "320df408978706136fa2e01b295b3b03625e5e9f", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-beta.8.tgz", "fileCount": 6, "integrity": "sha512-HBtsvhgpp+aD2yE6H/pZG8XLDQP6aWP75MidqPVmQrmjS586Zp+xsPWsRV9Nt8Gq9wJPkSIndYDi/drXfKRvXg==", "signatures": [{"sig": "MEUCIGsm2OaPf4XOM9rNCDfy+Fgm+JZ3K9HrN9mxPnA+ZgnUAiEAud2x/Umq96lxhEh4hBlKSrX3vKGoMm/8CA1fS7hoYsM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31242}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.0-beta.8"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-beta.8_1749023594395_0.10786061327646101", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/expect-utils", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/expect-utils@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "cb091e8a465d62abbacf36f9a7b02531cd613dac", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-NeEg6xhjEPxLhm3uO787UQb75xt2oXlb7Gc73D8DcGtTF+uFII/daLWDBQL3JwY/SXquXRc7L6dINEQa4VS5Cg==", "signatures": [{"sig": "MEUCIQDDQdbbQTvw4J14+PbIwZuE06QA6RYbqvo381OatmoqXQIgL5jir/fgwTHlNbZL7gasDyk1ndo/EBhHBi94c7F9JQg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31238}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.0-rc.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0-rc.1_1749430968883_0.695056021232018", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/expect-utils", "version": "30.0.0", "license": "MIT", "_id": "@jest/expect-utils@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "118d41d9df420db61d307308848a9e12f0fc1fad", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-UiWfsqNi/+d7xepfOv8KDcbbzcYtkWBe3a3kVDtg6M1kuN6CJ7b4HzIp5e1YHrSaQaVS8sdCoyCMCZClTLNKFQ==", "signatures": [{"sig": "MEQCIEACMNaXr5tjWyb4vlRJnla1E3d2/aJydONw+XMWD/STAiAle9L0JpjxBgHcD2QodqP7R0B3DRvnv9C1kc+jS1Xqyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31221}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@jest/get-type": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.0_1749521754657_0.6976800426487586", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/expect-utils", "version": "30.0.1", "license": "MIT", "_id": "@jest/expect-utils@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "e2c3dae1223dc45e658d83694bb5c7ffc7b14380", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.1.tgz", "fileCount": 6, "integrity": "sha512-txHSNST7ud1V7JVFS5N1qqU+Wf6tiFPxDbjQpklTnckeVecFF8O+LD6efgF5z1dBigp4nMmDIYYxslQJHaS7QA==", "signatures": [{"sig": "MEQCIB0Uz/X1b/vv2+y898H9IYxDMDovBp/OmGSgMCxMg27XAiBPjNsmWcFtQ/vJVWq3c8wTMWA0XdzQwUWSmbPTJBz2AQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31221}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"@jest/get-type": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.1_1750285892151_0.916468936294395", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/expect-utils", "version": "30.0.2", "license": "MIT", "_id": "@jest/expect-utils@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "d065f68c128cec526540193d88f2fc64c3d4f971", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.2.tgz", "fileCount": 6, "integrity": "sha512-FHF2YdtFBUQOo0/qdgt+6UdBFcNPF/TkVzcc+4vvf8uaBzUlONytGBeeudufIHHW1khRfM1sBbRT1VCK7n/0dQ==", "signatures": [{"sig": "MEUCIHUmyjBaS8BewiXoFAiTZ+Qh1NoT4VOU/EeYBLMhtFBIAiEAw4yx1TQphL3vjO2xLe65d7ugbH+Ifqb9H99WqG7SUh8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31221}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/expect-utils"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"@jest/get-type": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect-utils_30.0.2_1750329981373_0.17431032007884273", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.3": {"name": "@jest/expect-utils", "version": "30.0.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/expect-utils"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/get-type": "30.0.1"}, "devDependencies": {"immutable": "^5.1.2", "jest-matcher-utils": "30.0.3"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "d4a6c94daf4f6e63c949f2d0ed907aeaee840d2f", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/expect-utils@30.0.3", "dist": {"integrity": "sha512-SMtBvf2sfX2agcT0dA9pXwcUrKvOSDqBY4e4iRfT+Hya33XzV35YVg+98YQFErVGA/VR1Gto5Y2+A6G9LSQ3Yg==", "shasum": "2a9fb40110c8a13ae464da41f877df90d2e6bc3b", "tarball": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-30.0.3.tgz", "fileCount": 7, "unpackedSize": 33338, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF8CPXWXaBkBzsWQXmIgojCnB9JqiN25RCf6Rdr3/Vj2AiEA5RMgwedpXv+vNI9rYe9yJERXdecBwdDWV2EZnreQ6Dw="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/expect-utils_30.0.3_1750814512764_0.978460704892461"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-02-10T18:17:30.488Z", "modified": "2025-06-25T01:21:53.197Z", "28.0.0-alpha.0": "2022-02-10T18:17:30.682Z", "28.0.0-alpha.1": "2022-02-15T21:26:49.073Z", "28.0.0-alpha.2": "2022-02-16T18:11:57.089Z", "28.0.0-alpha.3": "2022-02-17T15:42:21.488Z", "28.0.0-alpha.4": "2022-02-22T12:13:54.601Z", "28.0.0-alpha.5": "2022-02-24T20:57:17.278Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.470Z", "28.0.0-alpha.7": "2022-03-06T10:02:40.079Z", "28.0.0-alpha.8": "2022-04-05T14:59:39.154Z", "28.0.0-alpha.9": "2022-04-19T10:59:14.146Z", "28.0.0": "2022-04-25T12:08:06.732Z", "28.0.1": "2022-04-26T10:02:32.402Z", "28.0.2": "2022-04-27T07:44:02.391Z", "28.1.0": "2022-05-06T10:48:53.292Z", "28.1.1": "2022-06-07T06:09:35.488Z", "28.1.3": "2022-07-13T14:12:25.987Z", "29.0.0-alpha.0": "2022-07-17T22:07:06.965Z", "29.0.0-alpha.1": "2022-08-04T08:23:28.648Z", "29.0.0-alpha.3": "2022-08-07T13:41:36.395Z", "29.0.0-alpha.4": "2022-08-08T13:05:34.879Z", "29.0.0-alpha.6": "2022-08-19T13:57:48.873Z", "29.0.0": "2022-08-25T12:33:28.447Z", "29.0.1": "2022-08-26T13:34:42.088Z", "29.0.2": "2022-09-03T10:48:19.636Z", "29.0.3": "2022-09-10T14:41:38.305Z", "29.1.0": "2022-09-28T07:37:45.002Z", "29.1.2": "2022-09-30T07:22:51.890Z", "29.2.0": "2022-10-14T09:13:54.341Z", "29.2.1": "2022-10-18T16:00:17.339Z", "29.2.2": "2022-10-24T20:24:07.451Z", "29.3.1": "2022-11-08T22:56:26.301Z", "29.4.0": "2023-01-24T10:55:56.545Z", "29.4.1": "2023-01-26T15:08:40.613Z", "29.4.2": "2023-02-07T13:45:37.610Z", "29.4.3": "2023-02-15T11:57:26.337Z", "29.5.0": "2023-03-06T13:33:37.154Z", "29.6.0": "2023-07-04T15:25:50.036Z", "29.6.1": "2023-07-06T14:18:29.960Z", "29.6.2": "2023-07-27T09:21:34.697Z", "29.6.3": "2023-08-21T12:39:21.955Z", "29.6.4": "2023-08-24T11:10:44.636Z", "29.7.0": "2023-09-12T06:43:47.243Z", "30.0.0-alpha.1": "2023-10-30T13:33:15.289Z", "30.0.0-alpha.2": "2023-11-16T09:28:33.730Z", "30.0.0-alpha.3": "2024-02-20T11:09:13.909Z", "30.0.0-alpha.4": "2024-05-12T21:43:31.790Z", "30.0.0-alpha.5": "2024-05-30T12:44:11.498Z", "30.0.0-alpha.6": "2024-08-08T07:43:09.193Z", "30.0.0-alpha.7": "2025-01-30T08:28:37.606Z", "30.0.0-beta.2": "2025-05-27T01:23:21.152Z", "30.0.0-beta.3": "2025-05-27T01:27:50.184Z", "30.0.0-beta.4": "2025-05-27T07:04:28.893Z", "30.0.0-beta.5": "2025-05-29T00:30:15.354Z", "30.0.0-beta.6": "2025-06-03T23:50:52.519Z", "30.0.0-beta.7": "2025-06-04T03:35:45.030Z", "30.0.0-beta.8": "2025-06-04T07:53:14.556Z", "30.0.0-rc.1": "2025-06-09T01:02:49.038Z", "30.0.0": "2025-06-10T02:15:54.828Z", "30.0.1": "2025-06-18T22:31:32.341Z", "30.0.2": "2025-06-19T10:46:21.530Z", "30.0.3": "2025-06-25T01:21:52.946Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/expect-utils"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}