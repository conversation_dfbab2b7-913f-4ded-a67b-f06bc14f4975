# Roles Support Ticket Management

## Overview
The roles support ticket system can now be easily enabled or disabled by administrators. When disabled, users will see a clear message explaining that the service is temporarily unavailable.

## Current Status
🔒 **ROLES SUPPORT IS CURRENTLY DISABLED**

Users attempting to create roles support tickets will receive:
```
❌ Roles Support is temporarily disabled

🔒 The roles support ticket system is currently closed for maintenance.

Please try again later or contact an administrator if you have an urgent roles-related issue.
```

## Management Commands

### `/toggle-roles-support` Command

#### Check Current Status
```
/toggle-roles-support action:status
```
Shows whether roles support is enabled or disabled and the current disabled message.

#### Enable Roles Support
```
/toggle-roles-support action:enable
```
Enables roles support tickets. Users will be able to create roles support tickets normally.

#### Disable Roles Support
```
/toggle-roles-support action:disable
```
Disables roles support tickets with the default message.

#### Disable with Custom Message
```
/toggle-roles-support action:disable message:Custom reason for disabling roles support
```
Disables roles support tickets with a custom message that users will see.

## Visual Indicators

### When Disabled
- Select menu option shows: `🔒 Roles Support | تقديم على رتبه (DISABLED)`
- Lock emoji (🔒) instead of user emoji
- Clear visual indication that the option is disabled

### When Enabled
- Select menu option shows: `Roles Support | تقديم على رتبه`
- Normal user emoji (<:user:1352804063422447684>)
- Standard appearance

## Technical Details

### Configuration File
The system uses `src/config/ticket-config.json` to store settings:

```json
{
  "rolesSupport": {
    "enabled": false,
    "disabledMessage": "❌ **Roles Support is temporarily disabled**\n\n🔒 The roles support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent roles-related issue."
  }
}
```

### How It Works

1. **Configuration Loading**: Both the ticket creation system and select menu dynamically load the configuration
2. **Real-time Updates**: Changes take effect immediately without restarting the bot
3. **User Experience**: Users get clear feedback when trying to use disabled features
4. **Admin Control**: Administrators can easily toggle the feature on/off

### Files Modified

1. `src/events/interactions/ticketHandler.js` - Added configuration loading and roles support check
2. `src/commands/moderation/ticket.js` - Dynamic select menu options based on configuration
3. `src/commands/admin/toggle-roles-support.js` - New admin command for managing roles support
4. `src/config/ticket-config.json` - Configuration file

## Usage Examples

### Scenario 1: Temporary Maintenance
```bash
# Disable for maintenance
/toggle-roles-support action:disable message:Roles support is temporarily down for system updates. Expected to be back online in 2 hours.

# Check status
/toggle-roles-support action:status

# Re-enable when ready
/toggle-roles-support action:enable
```

### Scenario 2: Policy Changes
```bash
# Disable with policy message
/toggle-roles-support action:disable message:Role applications are currently under review. New applications will be accepted starting next week.
```

### Scenario 3: Quick Toggle
```bash
# Quick disable with default message
/toggle-roles-support action:disable

# Quick enable
/toggle-roles-support action:enable
```

## Benefits

1. **Easy Management**: Simple commands to enable/disable
2. **Clear Communication**: Users know exactly why they can't create tickets
3. **Flexible Messaging**: Custom messages for different situations
4. **Visual Feedback**: Clear indicators in the interface
5. **No Restart Required**: Changes take effect immediately
6. **Persistent Settings**: Configuration survives bot restarts

## Permissions Required

- **Administrator** permission required to use `/toggle-roles-support`
- Only users with Administrator permission can enable/disable roles support

## Future Enhancements

The system can be easily extended to:
- Disable other ticket types
- Set scheduled enable/disable times
- Add role-based access to different ticket types
- Implement temporary disable with auto-enable timers
