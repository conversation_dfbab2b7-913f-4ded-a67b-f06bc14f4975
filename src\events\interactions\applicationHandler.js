const { <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits, ChannelType, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Load application configuration
function loadApplicationConfig() {
    const configPath = path.join(__dirname, '../../config/ticket-config.json');
    try {
        if (fs.existsSync(configPath)) {
            const data = fs.readFileSync(configPath, 'utf8');
            if (!data.trim()) {
                console.warn('Application config file is empty, using defaults');
                return getDefaultConfig();
            }
            const config = JSON.parse(data);
            return config.applicationTypes || getDefaultConfig().applicationTypes;
        }
    } catch (error) {
        console.error('Error loading application config:', error);
        console.warn('Using default configuration due to config file error');
        return getDefaultConfig().applicationTypes;
    }
    
    return getDefaultConfig().applicationTypes;
}

function getDefaultConfig() {
    return {
        applicationTypes: {
            staff: {
                enabled: true,
                disabledMessage: '❌ **Staff Applications are temporarily closed**\n\n🔒 We are not currently accepting staff applications.\n\nPlease check back later or contact an administrator for more information.',
                requiresApproval: true,
                maxApplications: 1
            },
            kick: {
                enabled: true,
                disabledMessage: '❌ **Kick Applications are temporarily closed**\n\n🔒 We are not currently accepting kick applications.\n\nPlease check back later or contact an administrator for more information.',
                requiresApproval: true,
                maxApplications: 1
            },
            event: {
                enabled: true,
                disabledMessage: '❌ **Event Applications are temporarily closed**\n\n🔒 We are not currently accepting event applications.\n\nPlease check back later or contact an administrator for more information.',
                requiresApproval: true,
                maxApplications: 1
            }
        }
    };
}

// Send log embed for application actions
async function sendApplicationLogEmbed(guild, action, user, applicationChannel, reason = null) {
    try {
        const logChannelName = 'application-logs';
        let logChannel = guild.channels.cache.find(channel => channel.name === logChannelName);
        
        if (!logChannel) {
            try {
                logChannel = await guild.channels.create({
                    name: logChannelName,
                    type: ChannelType.GuildText,
                    topic: 'Application system logs'
                });
            } catch (error) {
                console.error('Could not create application log channel:', error);
                return;
            }
        }

        const embed = new EmbedBuilder()
            .setTimestamp()
            .addFields(
                { name: '👤 User', value: `${user} (${user.tag})`, inline: true },
                { name: '📝 Application', value: `${applicationChannel}`, inline: true },
                { name: '⏰ Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            );

        switch (action) {
            case 'create':
                embed.setColor('#2ecc71')
                    .setTitle('📋 Application Created')
                    .setDescription('A new application has been submitted.');
                break;
            case 'approve':
                embed.setColor('#2ecc71')
                    .setTitle('✅ Application Approved')
                    .setDescription('An application has been approved.');
                if (reason) embed.addFields({ name: '📝 Reason', value: reason, inline: false });
                break;
            case 'reject':
                embed.setColor('#e74c3c')
                    .setTitle('❌ Application Rejected')
                    .setDescription('An application has been rejected.');
                if (reason) embed.addFields({ name: '📝 Reason', value: reason, inline: false });
                break;
            case 'close':
                embed.setColor('#95a5a6')
                    .setTitle('🔒 Application Closed')
                    .setDescription('An application has been closed.');
                if (reason) embed.addFields({ name: '📝 Reason', value: reason, inline: false });
                break;
        }

        await logChannel.send({ embeds: [embed] });
    } catch (error) {
        console.error('Error sending application log:', error);
    }
}

async function handleApplicationButton(interaction) {
    const applicationType = interaction.customId.replace('apply_', '');
    const config = loadApplicationConfig();
    
    // Check if application type is enabled
    if (!config[applicationType] || !config[applicationType].enabled) {
        return interaction.reply({
            content: config[applicationType]?.disabledMessage || '❌ This application type is currently disabled.',
            ephemeral: true
        });
    }

    // Check existing applications
    const username = interaction.user.username.toLowerCase();
    const existingApplications = interaction.guild.channels.cache.filter(channel => {
        const channelName = channel.name.toLowerCase();
        return channelName.startsWith(`app-${applicationType}-${username}`) && 
               !channelName.startsWith(`closed-app-${applicationType}-${username}`);
    });

    const maxApplications = config[applicationType].maxApplications || 1;
    if (existingApplications.size >= maxApplications) {
        const applicationList = existingApplications.map(channel => channel.toString()).join(', ');
        return interaction.reply({
            content: `❌ You already have the maximum number of ${applicationType} applications open (${existingApplications.size}/${maxApplications}):\n${applicationList}\n\nPlease wait for your current application to be reviewed before submitting a new one.`,
            ephemeral: true
        });
    }

    try {
        await interaction.deferReply({ ephemeral: true });

        // Get application roles from ticket system roles (reuse existing system)
        const ticketData = interaction.guild.channels.cache.find(
            channel => channel.topic && channel.topic.includes('ticket_roles:')
        );
        
        let applicationRoles = [];
        if (ticketData) {
            const roleIds = ticketData.topic.match(/ticket_roles:([^|]+)/)?.[1]?.split(',') || [];
            applicationRoles = roleIds.map(id => interaction.guild.roles.cache.get(id.trim())).filter(Boolean);
        }

        // If no ticket roles found, use default permissions
        if (applicationRoles.length === 0) {
            console.warn('No application review roles found, using default permissions');
        }

        // Create permission overwrites
        const permissionOverwrites = [
            {
                id: interaction.guild.roles.everyone,
                deny: [PermissionFlagsBits.ViewChannel]
            },
            {
                id: interaction.user.id,
                allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ReadMessageHistory]
            }
        ];

        // Add application review roles
        applicationRoles.forEach(role => {
            permissionOverwrites.push({
                id: role.id,
                allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ReadMessageHistory, PermissionFlagsBits.ManageMessages]
            });
        });

        // Generate unique application name
        let applicationNumber = '';
        if (existingApplications.size > 0) {
            applicationNumber = `-${existingApplications.size + 1}`;
        }

        // Create application channel
        const applicationChannel = await interaction.guild.channels.create({
            name: `app-${applicationType}-${interaction.user.username}${applicationNumber}`,
            type: ChannelType.GuildText,
            parent: interaction.channel.parent,
            permissionOverwrites: permissionOverwrites,
        });

        // Application type details
        const applicationDetails = {
            staff: {
                title: '👥 Staff Application',
                description: 'Thank you for your interest in joining our staff team!',
                color: '#3498db',
                questions: [
                    '**1. Personal Information:**\n• Age:\n• Timezone:\n• Discord experience:',
                    '**2. Experience:**\n• Previous moderation experience:\n• Why do you want to be staff:\n• What can you bring to the team:',
                    '**3. Availability:**\n• How many hours per day can you be active:\n• Any planned absences:',
                    '**4. Scenarios:**\n• How would you handle a heated argument:\n• What would you do if you saw rule breaking:'
                ]
            },
            kick: {
                title: '🦵 Kick Team Application',
                description: 'Thank you for applying to join our kick team!',
                color: '#e67e22',
                questions: [
                    '**1. Personal Information:**\n• Age:\n• Timezone:\n• Gaming experience:',
                    '**2. Skills:**\n• Kick streaming experience:\n• Content creation skills:\n• Technical abilities:',
                    '**3. Commitment:**\n• How often can you stream:\n• What type of content do you create:\n• Team collaboration experience:'
                ]
            },
            event: {
                title: '🎉 Event Team Application',
                description: 'Thank you for applying to join our event organization team!',
                color: '#9b59b6',
                questions: [
                    '**1. Personal Information:**\n• Age:\n• Timezone:\n• Event experience:',
                    '**2. Creativity:**\n• Event ideas you have:\n• Organization skills:\n• Leadership experience:',
                    '**3. Planning:**\n• How would you organize an event:\n• What makes a good event:\n• Availability for event planning:'
                ]
            }
        };

        const details = applicationDetails[applicationType];
        
        // Create application embed
        const applicationEmbed = new EmbedBuilder()
            .setColor(details.color)
            .setTitle(details.title)
            .setDescription(details.description)
            .addFields(
                { name: '👤 Applicant', value: `${interaction.user}`, inline: true },
                { name: '📝 Application ID', value: `#${applicationChannel.name}`, inline: true },
                { name: '⏰ Submitted At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                { name: '📋 Review Team', value: applicationRoles.length > 0 ? applicationRoles.map(role => role.toString()).join(', ') : 'Administrators', inline: false },
                { name: '📝 Please answer the following questions:', value: details.questions.join('\n\n'), inline: false },
                { name: '❗ Important', value: 'Please provide detailed and honest answers. Take your time to write thoughtful responses.', inline: false }
            )
            .setTimestamp();

        // Create action buttons for reviewers
        const actionButtons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`approve_application_${applicationType}`)
                    .setLabel('✅ Approve')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId(`reject_application_${applicationType}`)
                    .setLabel('❌ Reject')
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId(`close_application_${applicationType}`)
                    .setLabel('🔒 Close')
                    .setStyle(ButtonStyle.Secondary)
            );

        // Send application embed
        await applicationChannel.send({
            embeds: [applicationEmbed],
            components: [actionButtons]
        });

        // Log the application creation
        await sendApplicationLogEmbed(
            interaction.guild,
            'create',
            interaction.user,
            applicationChannel
        );

        await interaction.editReply({
            content: `✅ Your ${applicationType} application has been created successfully!\n\nPlease check ${applicationChannel} and answer all the questions thoroughly.`
        });

    } catch (error) {
        console.error('Error creating application:', error);
        await interaction.editReply({
            content: '❌ There was an error creating your application. Please try again later.'
        });
    }
}

module.exports = {
    handleApplicationButton,
    loadApplicationConfig,
    sendApplicationLogEmbed
};
