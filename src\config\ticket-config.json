﻿{
  "ticketTypes": {
    "roles_support": {
      "enabled": false,
      "disabledMessage": "❌ **Roles Support is temporarily disabled**\n\n🔒 The roles support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent roles-related issue."
    },
    "kick_support": {
      "enabled": true,
      "disabledMessage": "❌ **Kick Support is temporarily disabled**\n\n🔒 The kick support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent kick-related issue."
    },
    "staff_ticket": {
      "enabled": true,
      "disabledMessage": "❌ **Staff Tickets are temporarily disabled**\n\n🔒 The staff ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent staff-related issue."
    },
    "girls_support": {
      "enabled": true,
      "disabledMessage": "❌ **Girls Support is temporarily disabled**\n\n🔒 The girls support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent girls support issue."
    },
    "complaint": {
      "enabled": true,
      "disabledMessage": "❌ **Complaint System is temporarily disabled**\n\n🔒 The complaint ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent complaint."
    },
    "server_support": {
      "enabled": true,
      "disabledMessage": "❌ **Server Support is temporarily disabled**\n\n�� The server support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent server-related issue."
    }
  },
  "applicationTypes": {
    "staff": {
      "enabled": true,
      "disabledMessage": "❌ **Staff Applications are temporarily closed**\n\n🔒 We are not currently accepting staff applications.\n\nPlease check back later or contact an administrator for more information.",
      "requiresApproval": true,
      "maxApplications": 1
    },
    "kick": {
      "enabled": true,
      "disabledMessage": "❌ **Kick Applications are temporarily closed**\n\n🔒 We are not currently accepting kick applications.\n\nPlease check back later or contact an administrator for more information.",
      "requiresApproval": true,
      "maxApplications": 1
    },
    "event": {
      "enabled": true,
      "disabledMessage": "❌ **Event Applications are temporarily closed**\n\n🔒 We are not currently accepting event applications.\n\nPlease check back later or contact an administrator for more information.",
      "requiresApproval": true,
      "maxApplications": 1
    }
  }
}
