const { Slash<PERSON>ommandBuilder, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Load ticket configuration
function loadTicketConfig() {
    const configPath = path.join(__dirname, '../../config/ticket-config.json');
    try {
        if (fs.existsSync(configPath)) {
            const data = fs.readFileSync(configPath, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Error loading ticket config:', error);
    }

    // Default configuration if file doesn't exist
    return {
        ticketTypes: {
            roles_support: {
                enabled: false,
                disabledMessage: '❌ **Roles Support is temporarily disabled**\n\n🔒 The roles support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent roles-related issue.'
            },
            kick_support: {
                enabled: true,
                disabledMessage: '❌ **Kick Support is temporarily disabled**\n\n🔒 The kick support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent kick-related issue.'
            },
            staff_ticket: {
                enabled: true,
                disabledMessage: '❌ **Staff Tickets are temporarily disabled**\n\n🔒 The staff ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent staff-related issue.'
            },
            girls_support: {
                enabled: true,
                disabledMessage: '❌ **Girls Support is temporarily disabled**\n\n🔒 The girls support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent girls support issue.'
            },
            complaint: {
                enabled: true,
                disabledMessage: '❌ **Complaint System is temporarily disabled**\n\n🔒 The complaint ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent complaint.'
            },
            server_support: {
                enabled: true,
                disabledMessage: '❌ **Server Support is temporarily disabled**\n\n🔒 The server support ticket system is currently closed for maintenance.\n\nPlease try again later or contact an administrator if you have an urgent server-related issue.'
            }
        }
    };
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Manage ticket system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a ticket system')
                .addStringOption(option =>
                    option.setName('roles')
                        .setDescription('The roles that can see tickets (mention multiple roles separated by spaces)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all active tickets')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Filter by ticket type')
                        .setRequired(false)
                        .addChoices(
                            { name: 'All', value: 'all' },
                            { name: 'Roles Support', value: 'roles' },
                            { name: 'Kick Support', value: 'kick' },
                            { name: 'Staff Ticket', value: 'staff' },
                            { name: 'Girls Support', value: 'girls' },
                            { name: 'Complaint', value: 'complaint' },
                            { name: 'Server Support', value: 'server' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('close-type')
                .setDescription('Close all tickets of a specific type')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Type of tickets to close')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Roles Support', value: 'roles' },
                            { name: 'Kick Support', value: 'kick' },
                            { name: 'Staff Ticket', value: 'staff' },
                            { name: 'Girls Support', value: 'girls' },
                            { name: 'Complaint', value: 'complaint' },
                            { name: 'Server Support', value: 'server' }
                        ))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for closing tickets')
                        .setRequired(false))),

    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'create':
                await handleCreateTicketSystem(interaction, client);
                break;
            case 'list':
                await handleListTickets(interaction, client);
                break;
            case 'close-type':
                await handleCloseTicketsByType(interaction, client);
                break;
        }
    }
};

async function handleCreateTicketSystem(interaction, client) {
    const rolesString = interaction.options.getString('roles');

    const roleIds = rolesString.match(/<@&(\d+)>/g)?.map(mention => mention.match(/\d+/)[0]) || [];

    if (roleIds.length === 0) {
        return interaction.reply({
            content: 'Please mention at least one valid role!',
            ephemeral: true
        });
    }

    const validRoles = [];
    for (const roleId of roleIds) {
        const role = interaction.guild.roles.cache.get(roleId);
        if (role) validRoles.push(role);
    }

    if (validRoles.length === 0) {
        return interaction.reply({
            content: 'None of the mentioned roles were found!',
            ephemeral: true
        });
    }

    const rolesData = validRoles.map(role => role.id).join(',');

    // Load configuration to check ticket type statuses
    const config = loadTicketConfig();
    const ticketTypes = config.ticketTypes;

        const ticketEmbed = new EmbedBuilder()
            .setColor('#61607e')
            .setTitle('**🎫 نظام التذاكر | Ticket System**')
            .setDescription(`
<:1294025486296551496:1352763127426580540> مرحباً بك في قسم التيكت في سيرفر **SAHM Community** <:188d3ae0aeda472198d020d24fdf4db5:1352764164019453972>
إذا كنت بحاجة لدعم، استفسار، أو لديك مشكلة تحتاج حل، هذا هو المكان المناسب <:1294025486296551496:1352763127426580540>

<:1294025486296551496:1352763127426580540> كيفية استخدام التيكت :  
   <:1294025486296551496:1352763127426580540> 1 انقر على الزر الموجود في أي قسم تريد فتح تذكرة  
   <:1294025486296551496:1352763127426580540> 2 اكتب مشكلتك أو استفسارك بوضوح 
   <:1294025486296551496:1352763127426580540> 3 انتظر رد فريق الإدارة وسنساعدك في أقرب وقت 

<:1294025486296551496:1352763127426580540> شكراً لتفهمك وكونك جزءمن مجتمع **SAHM** <:188d3ae0aeda472198d020d24fdf4db5:1352764164019453972>

<:1294025486296551496:1352763127426580540> Welcome to the Ticket section in **SAHM Community** 
If you need a request, inquiry, or have a problem that needs to be solved, this is the right place. 

<:1294025486296551496:1352763127426580540> **How to use the ticket:**
<:1294025486296551496:1352763127426580540> **1** Click on the button on any section you want to open a ticket to 
<:1294025486296551496:1352763127426580540> **2** Write your problem or inquiry clearly 
<:1294025486296551496:1352763127426580540> **3** Please wait for a response — we’ll assist you soon.

<:1294025486296551496:1352763127426580540> **Thank you for your understanding and being part of the SAHM community ** <:188d3ae0aeda472198d020d24fdf4db5:1352764164019453972>`)
            .setFooter({ text: 'Ticket System • You can open up to 3 tickets at once' })
            .setTimestamp();

        const selectMenu = new ActionRowBuilder()
            .addComponents(
                new StringSelectMenuBuilder()
                    .setCustomId(`create_ticket_${rolesData}`)
                    .setPlaceholder('Select Ticket Type | اختر نوع التذكرة')
                    .addOptions([
                        {
                            label: ticketTypes.kick_support?.enabled !== false ? 'Kick Support | الدعم الفني للكيك' : '🔒 Kick Support | الدعم الفني للكيك (DISABLED)',
                            value: 'kick_support',
                            emoji: ticketTypes.kick_support?.enabled !== false ? '<:customersupport:1352794989821820948>' : '🔒'
                        },
                        {
                            label: ticketTypes.staff_ticket?.enabled !== false ? 'Staff Ticket | تكت ادارة' : '🔒 Staff Ticket | تكت ادارة (DISABLED)',
                            value: 'staff_ticket',
                            emoji: ticketTypes.staff_ticket?.enabled !== false ? '<:teamwork:1352793934920941638>' : '🔒'
                        },
                        {
                            label: ticketTypes.girls_support?.enabled !== false ? 'Girls Support | الدعم الفني للبنات' : '🔒 Girls Support | الدعم الفني للبنات (DISABLED)',
                            value: 'girls_support',
                            emoji: ticketTypes.girls_support?.enabled !== false ? '<:customerservice:1352793132592271472>' : '🔒'
                        },
                        {
                            label: ticketTypes.roles_support?.enabled === true ? 'Roles Support | تقديم على رتبه' : '🔒 Roles Support | تقديم على رتبه (DISABLED)',
                            value: 'roles_support',
                            emoji: ticketTypes.roles_support?.enabled === true ? '<:user:1352804063422447684>' : '🔒'
                        },
                        {
                            label: ticketTypes.complaint?.enabled !== false ? 'Complaint | رفع شكوى' : '🔒 Complaint | رفع شكوى (DISABLED)',
                            value: 'complaint',
                            emoji: ticketTypes.complaint?.enabled !== false ? '<:complain:1352794654931681320>' : '🔒'
                        },
                        {
                            label: ticketTypes.server_support?.enabled !== false ? 'Server Support | دعم الدس' : '🔒 Server Support | دعم الدس (DISABLED)',
                            value: 'server_support',
                            emoji: ticketTypes.server_support?.enabled !== false ? '<:technicalteam:1352804962047885332>' : '🔒'
                        }
                    ])
            );

        await interaction.channel.send({
            embeds: [ticketEmbed],
            components: [selectMenu]
        });

        await interaction.reply({
            content: '✅ Ticket system has been set up successfully!',
            ephemeral: true
        });
}

async function handleListTickets(interaction, client) {
    const typeFilter = interaction.options.getString('type') || 'all';

    // Get all channels that match ticket patterns
    const allChannels = interaction.guild.channels.cache;
    const ticketChannels = allChannels.filter(channel => {
        if (channel.type !== 0) return false; // Only text channels

        const channelName = channel.name.toLowerCase();

        // Check if it's a ticket channel (not closed)
        if (channelName.startsWith('closed-')) return false;

        // Check for different ticket types (including numbered tickets)
        const ticketPrefixes = ['roles-', 'kick-', 'staff-', 'girls-', 'complaint-', 'server-', 'ticket-', 'claimed-'];
        const isTicketChannel = ticketPrefixes.some(prefix => channelName.startsWith(prefix));

        if (!isTicketChannel) return false;

        // Apply type filter
        if (typeFilter !== 'all') {
            const matchesType = channelName.startsWith(`${typeFilter}-`) ||
                               channelName.startsWith(`claimed-${typeFilter}-`);
            return matchesType;
        }

        return true;
    });

    if (ticketChannels.size === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('🎫 Active Tickets')
            .setDescription(typeFilter === 'all' ? 'No active tickets found.' : `No active ${typeFilter} tickets found.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Group tickets by type
    const ticketsByType = {};
    ticketChannels.forEach(channel => {
        const channelName = channel.name.toLowerCase();
        let ticketType = 'general';

        if (channelName.startsWith('roles-') || channelName.startsWith('claimed-roles-')) ticketType = 'roles';
        else if (channelName.startsWith('kick-') || channelName.startsWith('claimed-kick-')) ticketType = 'kick';
        else if (channelName.startsWith('staff-') || channelName.startsWith('claimed-staff-')) ticketType = 'staff';
        else if (channelName.startsWith('girls-') || channelName.startsWith('claimed-girls-')) ticketType = 'girls';
        else if (channelName.startsWith('complaint-') || channelName.startsWith('claimed-complaint-')) ticketType = 'complaint';
        else if (channelName.startsWith('server-') || channelName.startsWith('claimed-server-')) ticketType = 'server';

        if (!ticketsByType[ticketType]) ticketsByType[ticketType] = [];
        ticketsByType[ticketType].push(channel);
    });

    const embed = new EmbedBuilder()
        .setColor('#61607e')
        .setTitle('🎫 Active Tickets')
        .setDescription(typeFilter === 'all' ? 'List of all active tickets:' : `List of active ${typeFilter} tickets:`)
        .setTimestamp();

    // Add fields for each ticket type
    Object.entries(ticketsByType).forEach(([type, channels]) => {
        const typeEmojis = {
            roles: '👤',
            kick: '🦵',
            staff: '👥',
            girls: '👩',
            complaint: '📝',
            server: '🖥️',
            general: '🎫'
        };

        const channelList = channels.map(channel => {
            const isClaimed = channel.name.startsWith('claimed-');
            return `${isClaimed ? '✋' : '🔓'} ${channel}`;
        }).join('\n');

        embed.addFields({
            name: `${typeEmojis[type] || '🎫'} ${type.charAt(0).toUpperCase() + type.slice(1)} Support (${channels.length})`,
            value: channelList || 'None',
            inline: false
        });
    });

    // Add action buttons for roles support tickets if any exist
    const rolesTickets = ticketsByType.roles || [];
    const components = [];

    if (rolesTickets.length > 0 && typeFilter === 'roles') {
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('close_all_roles_tickets')
                    .setLabel(`Close All Roles Tickets (${rolesTickets.length})`)
                    .setEmoji('🔒')
                    .setStyle(ButtonStyle.Danger)
            );
        components.push(actionRow);
    }

    await interaction.reply({ embeds: [embed], components, ephemeral: true });
}

async function handleCloseTicketsByType(interaction, client) {
    const ticketType = interaction.options.getString('type');
    const reason = interaction.options.getString('reason') || 'Temporarily closed by administrator';

    // Find all tickets of the specified type
    const allChannels = interaction.guild.channels.cache;
    const ticketsToClose = allChannels.filter(channel => {
        if (channel.type !== 0) return false; // Only text channels

        const channelName = channel.name.toLowerCase();

        // Check if it's an active ticket of the specified type (including numbered tickets)
        const matchesType = channelName.startsWith(`${ticketType}-`) ||
                           channelName.startsWith(`claimed-${ticketType}-`);
        const isNotClosed = !channelName.startsWith('closed-');

        return matchesType && isNotClosed;
    });

    if (ticketsToClose.size === 0) {
        return interaction.reply({
            content: `❌ No active ${ticketType} tickets found to close.`,
            ephemeral: true
        });
    }

    // Confirm before closing
    const confirmEmbed = new EmbedBuilder()
        .setColor('#e74c3c')
        .setTitle('⚠️ Confirm Bulk Ticket Closure')
        .setDescription(`Are you sure you want to close **${ticketsToClose.size}** ${ticketType} support tickets?\n\n**Reason:** ${reason}\n\n⚠️ This action cannot be undone.`)
        .addFields({
            name: 'Tickets to be closed:',
            value: ticketsToClose.map(channel => `• ${channel}`).join('\n').slice(0, 1000) + (ticketsToClose.size > 10 ? '\n...' : ''),
            inline: false
        });

    const confirmButtons = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`confirm_close_${ticketType}_tickets`)
                .setLabel('Confirm Close')
                .setEmoji('✅')
                .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
                .setCustomId('cancel_close_tickets')
                .setLabel('Cancel')
                .setEmoji('❌')
                .setStyle(ButtonStyle.Secondary)
        );

    await interaction.reply({
        embeds: [confirmEmbed],
        components: [confirmButtons],
        ephemeral: true
    });
}