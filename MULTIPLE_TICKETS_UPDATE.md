# Multiple Tickets Per User Update

## Overview
Updated the Discord bot's ticket system to allow users to open up to **3 tickets simultaneously** instead of the previous limit of 1 ticket per user.

## Key Changes Made

### 1. Ticket Creation Logic Updates

#### Simple Ticket System (`src/events/interactions/ticketHandler.js`)
- **Before**: Users could only have 1 ticket with name pattern `ticket-username`
- **After**: Users can have up to 3 tickets with names:
  - `ticket-username` (first ticket)
  - `ticket-username-2` (second ticket)
  - `ticket-username-3` (third ticket)

#### Select Menu Ticket System
- **Before**: Users could only have 1 ticket per type (e.g., `roles-username`)
- **After**: Users can have up to 3 tickets total across all types with names:
  - `roles-username` (first roles ticket)
  - `roles-username-2` (second roles ticket)
  - `kick-username` (first kick ticket)
  - etc.

### 2. Ticket Limit Enforcement

#### User Ticket Counting
- System now counts all active tickets for a user across all types
- Excludes closed tickets (those starting with `closed-`)
- Excludes claimed tickets from the count (they're still active but renamed)

#### Error Messages
When users try to create a 4th ticket, they receive:
```
❌ You already have the maximum number of tickets open (3/3):
#ticket-username, #roles-username-2, #kick-username

Please close one of your existing tickets before creating a new one.
```

### 3. Enhanced Ticket Information

#### Welcome Embeds
- Added "Your Active Tickets: X/3" field to show current ticket count
- Users can see how many tickets they have open

#### Ticket System Description
- Updated footer to mention "You can open up to 3 tickets at once"

### 4. Updated Ticket Management Commands

#### `/ticket list` Command
- Now properly handles numbered tickets
- Shows all tickets regardless of numbering scheme
- Groups tickets by type correctly

#### `/ticket close-type` Command
- Updated to handle numbered tickets when doing bulk closures
- Properly identifies tickets with numbers in their names

## Technical Implementation Details

### Ticket Naming Convention
- **First ticket**: `{type}-{username}` (e.g., `roles-johnsmith`)
- **Additional tickets**: `{type}-{username}-{number}` (e.g., `roles-johnsmith-2`)
- **Closed tickets**: `closed-{original-name}` (e.g., `closed-roles-johnsmith-2`)
- **Claimed tickets**: `claimed-{original-name}` (e.g., `claimed-roles-johnsmith`)

### Ticket Counting Logic
```javascript
// Count all active tickets for a user
const userTickets = guild.channels.cache.filter(channel => {
    const channelName = channel.name.toLowerCase();
    const username = user.username.toLowerCase();
    
    // Check for tickets with any prefix and the username
    const ticketPrefixes = ['ticket-', 'roles-', 'kick-', 'staff-', 'girls-', 'complaint-', 'server-'];
    const isUserTicket = ticketPrefixes.some(prefix => {
        return channelName.startsWith(`${prefix}${username}`) || 
               channelName.startsWith(`claimed-${prefix}${username}`);
    });
    
    // Exclude closed tickets
    const isClosed = channelName.startsWith('closed-');
    
    return isUserTicket && !isClosed;
});
```

### Number Generation Logic
```javascript
// For simple tickets
let ticketNumber = '';
if (userTickets.size > 0) {
    ticketNumber = `-${userTickets.size + 1}`;
}

// For typed tickets
const existingTicketsOfType = guild.channels.cache.filter(channel => {
    const channelName = channel.name.toLowerCase();
    return (channelName.startsWith(`${ticketPrefix}-${username}`) || 
            channelName.startsWith(`claimed-${ticketPrefix}-${username}`)) &&
           !channelName.startsWith(`closed-${ticketPrefix}-${username}`);
});

let ticketNumber = '';
if (existingTicketsOfType.size > 0) {
    ticketNumber = `-${existingTicketsOfType.size + 1}`;
}
```

## Benefits

1. **Improved User Experience**: Users can handle multiple issues simultaneously
2. **Better Organization**: Different ticket types can be opened concurrently
3. **Maintained Control**: 3-ticket limit prevents spam while allowing flexibility
4. **Backward Compatibility**: Existing single tickets continue to work
5. **Clear Communication**: Users always know their ticket count

## Usage Examples

### User Opens Multiple Tickets
1. User creates a roles support ticket → `roles-johnsmith`
2. User creates a kick support ticket → `kick-johnsmith`  
3. User creates another roles ticket → `roles-johnsmith-2`
4. User tries to create a 4th ticket → Gets error message with current tickets listed

### Admin Management
- `/ticket list type:roles` - Shows all roles tickets including numbered ones
- `/ticket close-type type:roles` - Closes all roles tickets (including numbered ones)
- `/ticket list` - Shows all active tickets with proper grouping

## Files Modified

1. `src/events/interactions/ticketHandler.js` - Main ticket creation logic
2. `src/commands/moderation/ticket.js` - Ticket management commands
3. `MULTIPLE_TICKETS_UPDATE.md` - This documentation file

## Testing Recommendations

1. Test creating multiple tickets as the same user
2. Test the 3-ticket limit enforcement
3. Test ticket listing with numbered tickets
4. Test bulk closure with numbered tickets
5. Verify closed ticket exclusion from counts
6. Test claimed ticket handling
