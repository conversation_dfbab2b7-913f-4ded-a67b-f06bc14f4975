const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { loadApplicationConfig, sendApplicationLogEmbed } = require('../../events/interactions/applicationHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('application')
        .setDescription('Manage application system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all active applications')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Filter by application type')
                        .setRequired(false)
                        .addChoices(
                            { name: 'All', value: 'all' },
                            { name: 'Staff Applications', value: 'staff' },
                            { name: 'Kick Applications', value: 'kick' },
                            { name: 'Event Applications', value: 'event' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('approve')
                .setDescription('Approve an application')
                .addChannelOption(option =>
                    option.setName('application')
                        .setDescription('The application channel to approve')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for approval (optional)')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reject')
                .setDescription('Reject an application')
                .addChannelOption(option =>
                    option.setName('application')
                        .setDescription('The application channel to reject')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for rejection')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('close')
                .setDescription('Close an application')
                .addChannelOption(option =>
                    option.setName('application')
                        .setDescription('The application channel to close')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for closing')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('View application statistics')),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'list':
                await handleListApplications(interaction);
                break;
            case 'approve':
                await handleApproveApplication(interaction);
                break;
            case 'reject':
                await handleRejectApplication(interaction);
                break;
            case 'close':
                await handleCloseApplication(interaction);
                break;
            case 'stats':
                await handleApplicationStats(interaction);
                break;
        }
    }
};

async function handleListApplications(interaction) {
    const typeFilter = interaction.options.getString('type') || 'all';
    
    // Get all application channels
    const allChannels = interaction.guild.channels.cache;
    const applicationChannels = allChannels.filter(channel => {
        if (channel.type !== 0) return false; // Only text channels
        
        const channelName = channel.name.toLowerCase();
        
        // Check if it's an application channel (not closed)
        if (channelName.startsWith('closed-app-')) return false;
        
        // Check for application pattern: app-type-username
        const isApplicationChannel = channelName.startsWith('app-');
        
        if (!isApplicationChannel) return false;
        
        // Apply type filter
        if (typeFilter !== 'all') {
            return channelName.startsWith(`app-${typeFilter}-`);
        }
        
        return true;
    });

    if (applicationChannels.size === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('📋 Active Applications')
            .setDescription(typeFilter === 'all' ? 'No active applications found.' : `No active ${typeFilter} applications found.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Group applications by type
    const applicationsByType = {};
    applicationChannels.forEach(channel => {
        const channelName = channel.name.toLowerCase();
        let applicationType = 'general';
        
        if (channelName.startsWith('app-staff-')) applicationType = 'staff';
        else if (channelName.startsWith('app-kick-')) applicationType = 'kick';
        else if (channelName.startsWith('app-event-')) applicationType = 'event';
        
        if (!applicationsByType[applicationType]) applicationsByType[applicationType] = [];
        applicationsByType[applicationType].push(channel);
    });

    const embed = new EmbedBuilder()
        .setColor('#3498db')
        .setTitle('📋 Active Applications')
        .setDescription(typeFilter === 'all' ? 'List of all active applications:' : `List of active ${typeFilter} applications:`)
        .setTimestamp();

    // Add fields for each application type
    Object.entries(applicationsByType).forEach(([type, channels]) => {
        const typeEmojis = {
            staff: '👥',
            kick: '🦵',
            event: '🎉',
            general: '📋'
        };
        
        const channelList = channels.map(channel => {
            // Extract username from channel name
            const parts = channel.name.split('-');
            const username = parts.length > 2 ? parts.slice(2).join('-') : 'unknown';
            return `📝 ${channel} (${username})`;
        }).join('\n');
        
        embed.addFields({
            name: `${typeEmojis[type] || '📋'} ${type.charAt(0).toUpperCase() + type.slice(1)} Applications (${channels.length})`,
            value: channelList || 'None',
            inline: false
        });
    });

    await interaction.reply({ embeds: [embed], ephemeral: true });
}

async function handleApproveApplication(interaction) {
    const applicationChannel = interaction.options.getChannel('application');
    const reason = interaction.options.getString('reason') || 'Application approved by staff';

    // Verify it's an application channel
    if (!applicationChannel.name.startsWith('app-')) {
        return interaction.reply({
            content: '❌ The specified channel is not an application channel.',
            ephemeral: true
        });
    }

    try {
        // Get applicant from channel permissions
        let applicantId = null;
        const channelPermissions = applicationChannel.permissionOverwrites.cache;
        channelPermissions.forEach((overwrite) => {
            if (overwrite.type === 1 && overwrite.allow.has(PermissionFlagsBits.ViewChannel)) {
                applicantId = overwrite.id;
            }
        });

        const applicant = applicantId ? await interaction.guild.members.fetch(applicantId) : null;

        // Create approval embed
        const approvalEmbed = new EmbedBuilder()
            .setColor('#2ecc71')
            .setTitle('✅ Application Approved')
            .setDescription('Congratulations! Your application has been approved.')
            .addFields(
                { name: '👤 Applicant', value: applicant ? `${applicant.user}` : 'Unknown', inline: true },
                { name: '✅ Approved By', value: `${interaction.user}`, inline: true },
                { name: '⏰ Approved At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                { name: '📝 Reason', value: reason, inline: false },
                { name: '📋 Next Steps', value: 'Please wait for further instructions from the administration team.', inline: false }
            )
            .setTimestamp();

        await applicationChannel.send({ embeds: [approvalEmbed] });

        // Log the approval
        await sendApplicationLogEmbed(
            interaction.guild,
            'approve',
            applicant ? applicant.user : { tag: 'Unknown', toString: () => 'Unknown User' },
            applicationChannel,
            reason
        );

        await interaction.reply({
            content: `✅ Application ${applicationChannel} has been approved successfully.`,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error approving application:', error);
        await interaction.reply({
            content: '❌ There was an error approving the application.',
            ephemeral: true
        });
    }
}

async function handleRejectApplication(interaction) {
    const applicationChannel = interaction.options.getChannel('application');
    const reason = interaction.options.getString('reason');

    // Verify it's an application channel
    if (!applicationChannel.name.startsWith('app-')) {
        return interaction.reply({
            content: '❌ The specified channel is not an application channel.',
            ephemeral: true
        });
    }

    try {
        // Get applicant from channel permissions
        let applicantId = null;
        const channelPermissions = applicationChannel.permissionOverwrites.cache;
        channelPermissions.forEach((overwrite) => {
            if (overwrite.type === 1 && overwrite.allow.has(PermissionFlagsBits.ViewChannel)) {
                applicantId = overwrite.id;
            }
        });

        const applicant = applicantId ? await interaction.guild.members.fetch(applicantId) : null;

        // Create rejection embed
        const rejectionEmbed = new EmbedBuilder()
            .setColor('#e74c3c')
            .setTitle('❌ Application Rejected')
            .setDescription('Unfortunately, your application has been rejected.')
            .addFields(
                { name: '👤 Applicant', value: applicant ? `${applicant.user}` : 'Unknown', inline: true },
                { name: '❌ Rejected By', value: `${interaction.user}`, inline: true },
                { name: '⏰ Rejected At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                { name: '📝 Reason', value: reason, inline: false },
                { name: '🔄 Reapplication', value: 'You may reapply in the future when you meet the requirements.', inline: false }
            )
            .setTimestamp();

        await applicationChannel.send({ embeds: [rejectionEmbed] });

        // Log the rejection
        await sendApplicationLogEmbed(
            interaction.guild,
            'reject',
            applicant ? applicant.user : { tag: 'Unknown', toString: () => 'Unknown User' },
            applicationChannel,
            reason
        );

        await interaction.reply({
            content: `❌ Application ${applicationChannel} has been rejected.`,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error rejecting application:', error);
        await interaction.reply({
            content: '❌ There was an error rejecting the application.',
            ephemeral: true
        });
    }
}

async function handleCloseApplication(interaction) {
    const applicationChannel = interaction.options.getChannel('application');
    const reason = interaction.options.getString('reason') || 'Application closed by staff';

    // Verify it's an application channel
    if (!applicationChannel.name.startsWith('app-')) {
        return interaction.reply({
            content: '❌ The specified channel is not an application channel.',
            ephemeral: true
        });
    }

    try {
        // Get applicant from channel permissions
        let applicantId = null;
        const channelPermissions = applicationChannel.permissionOverwrites.cache;
        channelPermissions.forEach((overwrite) => {
            if (overwrite.type === 1 && overwrite.allow.has(PermissionFlagsBits.ViewChannel)) {
                applicantId = overwrite.id;
            }
        });

        const applicant = applicantId ? await interaction.guild.members.fetch(applicantId) : null;

        // Remove applicant's permissions
        if (applicantId) {
            await applicationChannel.permissionOverwrites.edit(applicantId, {
                ViewChannel: false,
                SendMessages: false
            });
        }

        // Rename channel to closed
        const newName = `closed-${applicationChannel.name}`;
        await applicationChannel.setName(newName);

        // Find or create "Closed Applications" category
        let closedCategory = interaction.guild.channels.cache.find(
            channel => channel.type === 4 && channel.name.toLowerCase() === 'closed applications'
        );

        if (!closedCategory) {
            try {
                closedCategory = await interaction.guild.channels.create({
                    name: 'Closed Applications',
                    type: 4, // Category
                    position: 999
                });
            } catch (error) {
                console.error('Error creating closed applications category:', error);
            }
        }

        // Move to closed category
        if (closedCategory) {
            await applicationChannel.setParent(closedCategory);
        }

        // Create closure embed
        const closureEmbed = new EmbedBuilder()
            .setColor('#95a5a6')
            .setTitle('🔒 Application Closed')
            .setDescription('This application has been closed.')
            .addFields(
                { name: '👤 Applicant', value: applicant ? `${applicant.user}` : 'Unknown', inline: true },
                { name: '🔒 Closed By', value: `${interaction.user}`, inline: true },
                { name: '⏰ Closed At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                { name: '📝 Reason', value: reason, inline: false }
            )
            .setTimestamp();

        await applicationChannel.send({ embeds: [closureEmbed] });

        // Log the closure
        await sendApplicationLogEmbed(
            interaction.guild,
            'close',
            applicant ? applicant.user : { tag: 'Unknown', toString: () => 'Unknown User' },
            applicationChannel,
            reason
        );

        await interaction.reply({
            content: `🔒 Application ${applicationChannel} has been closed successfully.`,
            ephemeral: true
        });

    } catch (error) {
        console.error('Error closing application:', error);
        await interaction.reply({
            content: '❌ There was an error closing the application.',
            ephemeral: true
        });
    }
}

async function handleApplicationStats(interaction) {
    const allChannels = interaction.guild.channels.cache;
    
    // Count active applications
    const activeApplications = allChannels.filter(channel => 
        channel.type === 0 && channel.name.startsWith('app-') && !channel.name.startsWith('closed-app-')
    );
    
    // Count closed applications
    const closedApplications = allChannels.filter(channel => 
        channel.type === 0 && channel.name.startsWith('closed-app-')
    );

    // Group by type
    const stats = {
        staff: { active: 0, closed: 0 },
        kick: { active: 0, closed: 0 },
        event: { active: 0, closed: 0 }
    };

    activeApplications.forEach(channel => {
        const name = channel.name.toLowerCase();
        if (name.startsWith('app-staff-')) stats.staff.active++;
        else if (name.startsWith('app-kick-')) stats.kick.active++;
        else if (name.startsWith('app-event-')) stats.event.active++;
    });

    closedApplications.forEach(channel => {
        const name = channel.name.toLowerCase();
        if (name.includes('-app-staff-')) stats.staff.closed++;
        else if (name.includes('-app-kick-')) stats.kick.closed++;
        else if (name.includes('-app-event-')) stats.event.closed++;
    });

    const embed = new EmbedBuilder()
        .setColor('#3498db')
        .setTitle('📊 Application Statistics')
        .setDescription('Overview of all applications in the server')
        .addFields(
            {
                name: '👥 Staff Applications',
                value: `**Active:** ${stats.staff.active}\n**Closed:** ${stats.staff.closed}\n**Total:** ${stats.staff.active + stats.staff.closed}`,
                inline: true
            },
            {
                name: '🦵 Kick Applications',
                value: `**Active:** ${stats.kick.active}\n**Closed:** ${stats.kick.closed}\n**Total:** ${stats.kick.active + stats.kick.closed}`,
                inline: true
            },
            {
                name: '🎉 Event Applications',
                value: `**Active:** ${stats.event.active}\n**Closed:** ${stats.event.closed}\n**Total:** ${stats.event.active + stats.event.closed}`,
                inline: true
            },
            {
                name: '📈 Overall Statistics',
                value: `**Total Active:** ${activeApplications.size}\n**Total Closed:** ${closedApplications.size}\n**Grand Total:** ${activeApplications.size + closedApplications.size}`,
                inline: false
            }
        )
        .setTimestamp();

    await interaction.reply({ embeds: [embed], ephemeral: true });
}
