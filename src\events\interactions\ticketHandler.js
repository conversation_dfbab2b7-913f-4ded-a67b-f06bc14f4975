const { EmbedBuilder, PermissionFlagsBits, ChannelType, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

async function sendLogEmbed(guild, type, user, ticketChannel, additionalInfo = '') {
    const logChannel = guild.channels.cache.find(channel => channel.name === 'ticket-logs');
    if (!logChannel) return;

    const colors = {
        create: '#2ecc71',  // Green
        claim: '#3498db',   // Blue
        close: '#e74c3c'    // Red
    };

    const titles = {
        create: '🎫 Ticket Created',
        claim: '✋ Ticket Claimed',
        close: '🔒 Ticket Closed'
    };

    const logEmbed = new EmbedBuilder()
        .setColor(colors[type])
        .setTitle(titles[type])
        .addFields(
            { name: '👤 User', value: `${user}`, inline: true },
            { name: '📝 Ticket', value: `${ticketChannel}`, inline: true },
            { name: '⏰ Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
        )
        .setTimestamp();

    if (additionalInfo) {
        logEmbed.addFields({ name: '📌 Additional Info', value: additionalInfo, inline: false });
    }

    await logChannel.send({ embeds: [logEmbed] });
}

module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        if (!interaction.isStringSelectMenu() && !interaction.isButton()) return;

        const logChannel = interaction.guild.channels.cache.find(channel => channel.name === 'ticket-logs');
        if (!logChannel) return;

        // Handle simple ticket button
        if (interaction.customId.startsWith('create_simple_ticket_') && interaction.isButton()) {
            try {
                const roleIds = interaction.customId.split('_')[3].split(',');
                const supportRoles = roleIds.map(id => interaction.guild.roles.cache.get(id)).filter(role => role !== undefined);

                if (supportRoles.length === 0) {
                    return interaction.reply({
                        content: '❌ The support roles for this ticket system no longer exist. Please contact an administrator.',
                        ephemeral: true
                    });
                }

                const existingTicket = interaction.guild.channels.cache.find(
                    channel => channel.name === `ticket-${interaction.user.username.toLowerCase()}`
                );

                if (existingTicket) {
                    return interaction.reply({
                        content: `You already have a ticket open at ${existingTicket}`,
                        ephemeral: true
                    });
                }

                const permissionOverwrites = [
                    {
                        id: interaction.guild.roles.everyone,
                        deny: [PermissionFlagsBits.ViewChannel],
                    },
                    {
                        id: interaction.user.id,
                        allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ReadMessageHistory],
                    }
                ];

                // Add support roles to permission overwrites
                supportRoles.forEach(role => {
                    permissionOverwrites.push({
                        id: role.id,
                        allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ReadMessageHistory, PermissionFlagsBits.ManageMessages],
                    });
                });

                const ticketChannel = await interaction.guild.channels.create({
                    name: `ticket-${interaction.user.username}`,
                    type: ChannelType.GuildText,
                    parent: interaction.channel.parent,
                    permissionOverwrites: permissionOverwrites,
                });

                const welcomeEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('🎫 Support Ticket Created')
                    .setDescription('Thank you for creating a support ticket. Our team will assist you shortly.')
                    .addFields(
                        { name: '👤 Ticket Creator', value: `${interaction.user}`, inline: true },
                        { name: '📝 Ticket ID', value: `#${ticketChannel.name}`, inline: true },
                        { name: '⏰ Created At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '📋 Support Team', value: supportRoles.map(role => role.toString()).join(', '), inline: false }
                    )
                    .setTimestamp();

                const rulesEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('📋 Ticket Guidelines')
                    .setDescription(`
يرجى كتابة التفاصيل التالية:

✔️ **الموضوع:**

✔️ **اللعبة:**

✔️ **الفعالية:**

📌 عند اكتمال الرد، سيتم إضافة مشاركتك.
                    `);

                const buttons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('close_ticket')
                            .setLabel('Close Ticket')
                            .setEmoji('🔒')
                            .setStyle(ButtonStyle.Danger),
                        new ButtonBuilder()
                            .setCustomId('claim_ticket')
                            .setLabel('Claim Ticket')
                            .setEmoji('✋')
                            .setStyle(ButtonStyle.Success)
                    );

                const supportRolesMention = supportRoles.map(role => role.toString()).join(' ');
                const contentMessage = `${interaction.user} ${supportRolesMention}`;

                await ticketChannel.send({
                    content: contentMessage,
                    embeds: [welcomeEmbed, rulesEmbed],
                    components: [buttons]
                });

                await interaction.reply({
                    content: `✅ Your support ticket has been created successfully! Please check ${ticketChannel}`,
                    ephemeral: true
                });

                await sendLogEmbed(
                    interaction.guild,
                    'create',
                    interaction.user,
                    ticketChannel,
                    'Type: Simple Support Ticket'
                );

            } catch (error) {
                console.error('Error creating simple ticket:', error);
                await interaction.reply({
                    content: '❌ There was an error creating your ticket. Please try again later.',
                    ephemeral: true
                });
            }
        }

        // Handle select menu for ticket creation
        if (interaction.customId.startsWith('create_ticket_') && interaction.isStringSelectMenu()) {
            try {
                const roleIds = interaction.customId.split('_')[2].split(',');
                const supportRoles = roleIds.map(id => interaction.guild.roles.cache.get(id)).filter(role => role !== undefined);

                if (supportRoles.length === 0) {
                    return interaction.reply({
                        content: '❌ The support roles for this ticket system no longer exist. Please contact an administrator.',
                        ephemeral: true
                    });
                }

                const existingTicket = interaction.guild.channels.cache.find(
                    channel => channel.name === `ticket-${interaction.user.username.toLowerCase()}`
                );

                if (existingTicket) {
                    return interaction.reply({
                        content: `You already have a ticket open at ${existingTicket}`,
                        ephemeral: true
                    });
                }

                const selectedOption = interaction.values[0];
                let ticketPrefix;
                switch (selectedOption) {
                    case 'kick_support': ticketPrefix = 'kick'; break;
                    case 'staff_ticket': ticketPrefix = 'staff'; break;
                    case 'girls_support': ticketPrefix = 'girls'; break;
                    case 'roles_support': ticketPrefix = 'roles'; break;
                    case 'complaint': ticketPrefix = 'complaint'; break;
                    case 'server_support': ticketPrefix = 'server'; break;
                    default: ticketPrefix = 'ticket';
                }

                // Find the girls support role first
                const girlsSupportRole = supportRoles.find(role =>
                    role.name.toLowerCase().includes('girls') ||
                    role.name.toLowerCase().includes('بنات')
                );

                // Special handling for different ticket types
                let rolesToUse = supportRoles;
                let rolesToMention = supportRoles;

                if (selectedOption === 'girls_support') {
                    // Girls support tickets: Only girls support role can see and gets tagged
                    if (girlsSupportRole) {
                        rolesToUse = [girlsSupportRole]; // Only girls support role can access
                        rolesToMention = [girlsSupportRole]; // Only girls support role gets tagged
                    } else {
                        // If no girls support role found, inform the user
                        return interaction.reply({
                            content: '❌ No Girls Support role found in the configured roles. Please contact an administrator.',
                            ephemeral: true
                        });
                    }
                } else {
                    // All other tickets: Only non-girls support roles can see and get tagged
                    rolesToUse = supportRoles.filter(role => role.id !== girlsSupportRole?.id); // Exclude girls support from access
                    rolesToMention = supportRoles.filter(role => role.id !== girlsSupportRole?.id); // Exclude girls support from mentions
                }

                const permissionOverwrites = [
                    {
                        id: interaction.guild.id,
                        deny: [PermissionFlagsBits.ViewChannel],
                    },
                    {
                        id: interaction.user.id,
                        allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages],
                    },
                    ...rolesToUse.map(role => ({
                        id: role.id,
                        allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.SendMessages, PermissionFlagsBits.ManageMessages],
                    }))
                ];

                const ticketChannel = await interaction.guild.channels.create({
                    name: `${ticketPrefix}-${interaction.user.username}`,
                    type: ChannelType.GuildText,
                    parent: interaction.channel.parent,
                    permissionOverwrites: permissionOverwrites,
                });

                const welcomeEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('🎫 New Support Ticket')
                    .setDescription('Thank you for creating a ticket. Our support team will assist you shortly.')
                    .addFields(
                        { name: '👤 Ticket Creator', value: `${interaction.user}`, inline: true },
                        { name: '📝 Ticket ID', value: `#${ticketChannel.name}`, inline: true },
                        { name: '📋 Ticket Type', value: interaction.component.options.find(opt => opt.value === selectedOption).label, inline: true },
                        { name: '⏰ Created At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                        { name: '📋 Support Team', value: rolesToUse.map(role => role.toString()).join(', '), inline: false },
                        { name: '❗ Important', value: 'Please provide as much detail as possible about your issue/request.', inline: false }
                    )
                    .setThumbnail(interaction.user.displayAvatarURL({ dynamic: true }))
                    .setFooter({ text: `${interaction.guild.name} • Support System`, iconURL: interaction.guild.iconURL({ dynamic: true }) });

                const rulesEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('📜 Ticket Guidelines')
                    .setDescription('يرجى اتباع هذه الإرشادات أثناء استخدام نظام التذاكر:')
                    .addFields(
                        { name: '1️⃣ خليك محترم', value: 'عامل كل أعضاء فريق الدعم باحترام.' },
                        { name: '2️⃣ كن صبورًا', value: 'فريقنا سوف يرد عليك بأسرع وقت ممكن.' },
                        { name: '3️⃣ خليك واضح', value: 'شرح مشكلتك بوضوح وقدم التفاصيل اللازمة.' },
                        { name: '4️⃣  ممنوع الاسبام', value: 'لا ترسل رسائل كثيرة أو تزعج الطاقم بدون سبب.' }
                    );

                const buttons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('close_ticket')
                            .setLabel('Close Ticket')
                            .setEmoji('🔒')
                            .setStyle(ButtonStyle.Danger),
                        new ButtonBuilder()
                            .setCustomId('claim_ticket')
                            .setLabel('Claim Ticket')
                            .setEmoji('✋')
                            .setStyle(ButtonStyle.Success)
                    );

                // Tag the appropriate roles based on ticket type
                let contentMessage = `${interaction.user}`;
                if (rolesToMention.length > 0) {
                    const supportRolesMention = rolesToMention.map(role => role.toString()).join(' ');
                    contentMessage += ` ${supportRolesMention}`;
                }

                await ticketChannel.send({
                    content: contentMessage,
                    embeds: [welcomeEmbed, rulesEmbed],
                    components: [buttons]
                });

                await interaction.reply({
                    content: `✅تم إنشاء تذكرتك بنجاح! الرجاء التحقق. ${ticketChannel}`,
                    ephemeral: true
                });

                await sendLogEmbed(
                    interaction.guild,
                    'create',
                    interaction.user,
                    ticketChannel,
                    `Type: ${interaction.component.options.find(opt => opt.value === selectedOption).label}`
                );

            } catch (error) {
                console.error('Error creating ticket:', error);
                await interaction.reply({
                    content: '❌ There was an error creating your ticket. Please try again later.',
                    ephemeral: true
                });
            }
        }

        // Handle button interactions
        if (interaction.isButton()) {
            // Get the authorized roles from the ticket channel's permission overwrites
            let authorizedRoleIds = [];

            // Extract role IDs from channel permission overwrites (these are the roles set when creating the ticket)
            const channelPermissions = interaction.channel.permissionOverwrites.cache;
            channelPermissions.forEach((overwrite) => {
                // If it's a role (not user) and has ViewChannel permission, it's an authorized role
                if (overwrite.type === 0 && overwrite.allow.has(PermissionFlagsBits.ViewChannel)) {
                    authorizedRoleIds.push(overwrite.id);
                }
            });

            // Check if user has permission to use ticket controls
            const hasTicketPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator) ||
                                      interaction.member.roles.cache.some(role => authorizedRoleIds.includes(role.id));

            if (interaction.customId === 'claim_ticket') {
                try {
                    // Check permissions
                    if (!hasTicketPermission) {
                        return interaction.reply({
                            content: '❌ You do not have permission to claim tickets. Only staff members can claim tickets.',
                            ephemeral: true
                        });
                    }

                    const claimedEmbed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🎫 Ticket Claimed')
                        .setDescription(`This ticket has been claimed by ${interaction.user}`)
                        .setTimestamp();

                    await interaction.reply({
                        embeds: [claimedEmbed]
                    });

                    await interaction.channel.setName(`claimed-${interaction.channel.name.split('-')[1]}`);

                    await sendLogEmbed(
                        interaction.guild,
                        'claim',
                        interaction.user,
                        interaction.channel,
                        'Ticket claimed by staff member'
                    );

                } catch (error) {
                    console.error('Error claiming ticket:', error);
                    await interaction.reply({
                        content: '❌ There was an error claiming the ticket.',
                        ephemeral: true
                    });
                }
            }

            if (interaction.customId === 'close_ticket') {
                try {
                    // Check permissions
                    if (!hasTicketPermission) {
                        return interaction.reply({
                            content: '❌ You do not have permission to close tickets. Only staff members can close tickets.',
                            ephemeral: true
                        });
                    }

                    const confirmEmbed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🔒 Close Ticket')
                        .setDescription('هل أنت متأكد أنك تريد إغلاق هذه التذكرة؟\nلا يمكن التراجع عن هذا الإجراء.')
                        .setFooter({ text: 'ستتم حذف التذكرة بعد التأكيد' });

                    const confirmButtons = new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId('confirm_close')
                                .setLabel('تأكيد الإغلاق')
                                .setEmoji('✅')
                                .setStyle(ButtonStyle.Danger),
                            new ButtonBuilder()
                                .setCustomId('cancel_close')
                                .setLabel('إلغاء')
                                .setEmoji('❌')
                                .setStyle(ButtonStyle.Secondary)
                        );

                    await interaction.reply({
                        embeds: [confirmEmbed],
                        components: [confirmButtons]
                    });
                } catch (error) {
                    console.error('خطأ في إغلاق التذكرة:', error);
                    await interaction.reply({
                        content: '❌ حدث خطأ أثناء إغلاق التذكرة. يرجى المحاولة لاحقًا.',
                        ephemeral: true
                    });
                }
            }

            if (interaction.customId === 'confirm_close') {
                try {
                    // Check permissions again for confirm close
                    if (!hasTicketPermission) {
                        return interaction.reply({
                            content: '❌ You do not have permission to close tickets. Only staff members can close tickets.',
                            ephemeral: true
                        });
                    }

                    // Find the ticket creator from channel permissions
                    let ticketCreatorId = null;
                    const channelPermissions = interaction.channel.permissionOverwrites.cache;
                    channelPermissions.forEach((overwrite) => {
                        // If it's a user (type 1) and has ViewChannel permission, it's likely the ticket creator
                        if (overwrite.type === 1 && overwrite.allow.has(PermissionFlagsBits.ViewChannel)) {
                            ticketCreatorId = overwrite.id;
                        }
                    });

                    // Remove the ticket creator's permissions (they can no longer see the channel)
                    if (ticketCreatorId) {
                        await interaction.channel.permissionOverwrites.edit(ticketCreatorId, {
                            ViewChannel: false,
                            SendMessages: false
                        });
                    }

                    // Get the username from the channel name for renaming
                    const channelNameParts = interaction.channel.name.split('-');
                    const username = channelNameParts.length > 1 ? channelNameParts.slice(1).join('-') : 'unknown';

                    // Rename the channel to closed-username
                    await interaction.channel.setName(`closed-${username}`);

                    // Find or create "Closed Tickets" category
                    let closedCategory = interaction.guild.channels.cache.find(
                        channel => channel.type === 4 && channel.name.toLowerCase() === 'closed tickets'
                    );

                    if (!closedCategory) {
                        closedCategory = await interaction.guild.channels.create({
                            name: 'Closed Tickets',
                            type: 4, // Category type
                        });
                    }

                    // Move the channel to the closed tickets category
                    await interaction.channel.setParent(closedCategory);

                    await sendLogEmbed(
                        interaction.guild,
                        'close',
                        interaction.user,
                        interaction.channel,
                        'Ticket closed and archived (user removed from channel)'
                    );

                    const closedEmbed = new EmbedBuilder()
                        .setColor('#e74c3c')
                        .setTitle('🔒 Ticket Closed')
                        .setDescription('This ticket has been closed and archived. The ticket creator has been removed from the channel.')
                        .addFields(
                            { name: '🔒 Closed By', value: `${interaction.user}`, inline: true },
                            { name: '⏰ Closed At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                            { name: '📁 Status', value: 'Archived - Messages Preserved', inline: true }
                        )
                        .setTimestamp();

                    await interaction.reply({
                        embeds: [closedEmbed]
                    });

                } catch (error) {
                    console.error('خطأ في إغلاق التذكرة:', error);
                    await interaction.reply({
                        content: '❌ حدث خطأ أثناء إغلاق التذكرة.',
                        ephemeral: true
                    });
                }
            }

            if (interaction.customId === 'cancel_close') {
                try {
                    const cancelEmbed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('✋ تم إلغاء إغلاق التذكرة')
                        .setDescription('ستظل التذكرة مفتوحة.')
                        .setTimestamp();

                    await interaction.message.delete();
                    await interaction.reply({
                        embeds: [cancelEmbed],
                        ephemeral: true
                    });
                } catch (error) {
                    console.error('خطأ في إلغاء إغلاق التذكرة:', error);
                    await interaction.reply({
                        content: '❌ حدث خطأ أثناء إلغاء إغلاق التذكرة.',
                        ephemeral: true
                    });
                }
            }

            // Handle bulk ticket closure confirmation
            if (interaction.customId.startsWith('confirm_close_') && interaction.customId.endsWith('_tickets')) {
                try {
                    const ticketType = interaction.customId.replace('confirm_close_', '').replace('_tickets', '');

                    // Check permissions
                    if (!hasTicketPermission) {
                        return interaction.reply({
                            content: '❌ You do not have permission to close tickets. Only staff members can close tickets.',
                            ephemeral: true
                        });
                    }

                    // Find all tickets of the specified type
                    const allChannels = interaction.guild.channels.cache;
                    const ticketsToClose = allChannels.filter(channel => {
                        if (channel.type !== 0) return false; // Only text channels

                        const channelName = channel.name.toLowerCase();

                        // Check if it's an active ticket of the specified type
                        return (channelName.startsWith(`${ticketType}-`) || channelName.startsWith(`claimed-${ticketType}-`))
                               && !channelName.startsWith('closed-');
                    });

                    if (ticketsToClose.size === 0) {
                        return interaction.reply({
                            content: `❌ No active ${ticketType} tickets found to close.`,
                            ephemeral: true
                        });
                    }

                    await interaction.deferReply({ ephemeral: true });

                    let closedCount = 0;
                    let errorCount = 0;

                    // Find or create "Closed Tickets" category
                    let closedCategory = interaction.guild.channels.cache.find(
                        channel => channel.type === 4 && channel.name.toLowerCase() === 'closed tickets'
                    );

                    if (!closedCategory) {
                        try {
                            closedCategory = await interaction.guild.channels.create({
                                name: 'Closed Tickets',
                                type: 4, // Category
                                position: 999
                            });
                        } catch (error) {
                            console.error('Error creating closed tickets category:', error);
                        }
                    }

                    // Close each ticket
                    for (const ticketChannel of ticketsToClose.values()) {
                        try {
                            // Get ticket creator from channel permissions
                            let ticketCreatorId = null;
                            const channelPermissions = ticketChannel.permissionOverwrites.cache;
                            channelPermissions.forEach((overwrite) => {
                                if (overwrite.type === 1 && overwrite.allow.has(PermissionFlagsBits.ViewChannel)) {
                                    ticketCreatorId = overwrite.id;
                                }
                            });

                            // Remove the ticket creator's permissions
                            if (ticketCreatorId) {
                                await ticketChannel.permissionOverwrites.edit(ticketCreatorId, {
                                    ViewChannel: false,
                                    SendMessages: false
                                });
                            }

                            // Get the username from the channel name for renaming
                            const channelNameParts = ticketChannel.name.split('-');
                            const username = channelNameParts.length > 1 ? channelNameParts.slice(1).join('-') : 'unknown';

                            // Rename the channel to closed-username
                            await ticketChannel.setName(`closed-${username}`);

                            // Move to closed category if it exists
                            if (closedCategory) {
                                await ticketChannel.setParent(closedCategory);
                            }

                            // Send closure message
                            const closedEmbed = new EmbedBuilder()
                                .setColor('#e74c3c')
                                .setTitle('🔒 Ticket Closed (Bulk Operation)')
                                .setDescription('This ticket has been closed temporarily by an administrator.')
                                .addFields(
                                    { name: '🔒 Closed By', value: `${interaction.user}`, inline: true },
                                    { name: '⏰ Closed At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                                    { name: '📁 Status', value: 'Temporarily Closed', inline: true },
                                    { name: '📝 Reason', value: 'Bulk closure of ' + ticketType + ' support tickets', inline: false }
                                )
                                .setTimestamp();

                            await ticketChannel.send({ embeds: [closedEmbed] });

                            // Log the closure
                            await sendLogEmbed(
                                interaction.guild,
                                'close',
                                interaction.user,
                                ticketChannel,
                                `Bulk closure - ${ticketType} support tickets`
                            );

                            closedCount++;
                        } catch (error) {
                            console.error(`Error closing ticket ${ticketChannel.name}:`, error);
                            errorCount++;
                        }
                    }

                    const resultEmbed = new EmbedBuilder()
                        .setColor(errorCount > 0 ? '#f39c12' : '#2ecc71')
                        .setTitle('🔒 Bulk Ticket Closure Complete')
                        .setDescription(`Successfully closed **${closedCount}** ${ticketType} support tickets.`)
                        .setTimestamp();

                    if (errorCount > 0) {
                        resultEmbed.addFields({
                            name: '⚠️ Errors',
                            value: `${errorCount} tickets could not be closed due to errors.`,
                            inline: false
                        });
                    }

                    await interaction.editReply({ embeds: [resultEmbed] });

                } catch (error) {
                    console.error('Error in bulk ticket closure:', error);
                    await interaction.editReply({
                        content: '❌ An error occurred while closing tickets.',
                    });
                }
            }

            // Handle cancel bulk closure
            if (interaction.customId === 'cancel_close_tickets') {
                try {
                    const cancelEmbed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('✋ Bulk Closure Cancelled')
                        .setDescription('No tickets were closed.')
                        .setTimestamp();

                    await interaction.reply({
                        embeds: [cancelEmbed],
                        ephemeral: true
                    });
                } catch (error) {
                    console.error('Error cancelling bulk closure:', error);
                    await interaction.reply({
                        content: '❌ An error occurred.',
                        ephemeral: true
                    });
                }
            }
        }
    },
};



